#!/usr/bin/env python3
"""只转换标签文件的脚本"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from txt_to_npy import TxtToNpyConverter

def main():
    """主函数 - 只转换标签"""
    converter = TxtToNpyConverter()
    
    print("="*80)
    print("Starting label conversion only...")
    print("="*80)
    
    # 只转换标签文件
    converter.convert_labels()
    
    # 输出统计信息
    print("="*80)
    print("Label Conversion Summary:")
    print(f"  Labels: {converter.stats['converted_labels']}/{converter.stats['total_labels']} converted")
    print(f"  Failed labels: {converter.stats['failed_labels']}")
    print("="*80)

if __name__ == "__main__":
    main() 