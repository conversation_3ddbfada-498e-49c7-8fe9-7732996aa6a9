"""数据加载和预处理工具"""
import os
import numpy as np
import json
import re
from typing import Dict, List, Tuple, Optional
from pathlib import Path

class ViewIdentifier:
    """视角识别器，用于从文件路径中提取视角信息"""
    
    def __init__(self):
        self.cam_pattern = re.compile(r"_cam(\d+)")
        self.webcam_pattern = re.compile(r"_webcam(\d+)")
        self.stereo_pattern = re.compile(r"_stereo(\d+)")
        self.view_pattern = re.compile(r"_view(\d+)")
    
    def extract_view_id(self, file_path: str) -> int:
        """从文件路径中提取视角编号"""
        # 优先匹配 webcam 模式
        webcam_match = self.webcam_pattern.search(file_path)
        if webcam_match:
            return int(webcam_match.group(1))

        # 其次匹配 cam 模式
        cam_match = self.cam_pattern.search(file_path)
        if cam_match:
            return int(cam_match.group(1))

        # 匹配 stereo 模式
        stereo_match = self.stereo_pattern.search(file_path)
        if stereo_match:
            return int(stereo_match.group(1)) + 100  # 给stereo一个不同的范围

        # 最后匹配 view 模式
        view_match = self.view_pattern.search(file_path)
        if view_match:
            return int(view_match.group(1))

        # 默认视角编号
        return 0
    
    def sort_by_view(self, file_paths: List[str]) -> List[str]:
        """按视角编号对文件路径进行排序"""
        return sorted(file_paths, key=self.extract_view_id)

class FrameSynchronizer:
    """帧同步器，确保多视角数据在时间维度上对齐"""
    
    @staticmethod
    def find_common_frames(view_features: List[np.ndarray]) -> List[int]:
        """找到所有视角共同存在的帧索引"""
        if not view_features:
            return []
        
        # 获取第一个视角的帧索引作为基准
        common_frames = set(view_features[0][:, 0].astype(int))
        
        # 与其他视角的帧索引求交集
        for features in view_features[1:]:
            frame_indices = set(features[:, 0].astype(int))
            common_frames &= frame_indices
        
        return sorted(list(common_frames))
    
    @staticmethod
    def sync_features(view_features: List[np.ndarray], common_frames: List[int]) -> List[np.ndarray]:
        """根据共同帧索引同步特征数据"""
        synced_features = []
        
        for features in view_features:
            # 创建帧索引到行的映射
            frame_to_row = {int(row[0]): row[1:] for row in features}
            
            # 按共同帧索引提取特征
            synced_data = []
            for frame_idx in common_frames:
                if frame_idx in frame_to_row:
                    synced_data.append(frame_to_row[frame_idx])
                else:
                    # 如果某帧缺失，用零向量填充
                    synced_data.append(np.zeros(64))
            
            synced_features.append(np.array(synced_data))
        
        return synced_features

class MultiViewFusion:
    """多视角特征融合器"""
    
    def __init__(self, fusion_method: str = "mean"):
        self.fusion_method = fusion_method
        self.view_identifier = ViewIdentifier()
        self.frame_synchronizer = FrameSynchronizer()
    
    def fuse_views(self, view_features: List[np.ndarray]) -> np.ndarray:
        """融合多个视角的特征"""
        if not view_features:
            return np.array([])

        if len(view_features) == 1:
            # 单视角情况，直接返回特征部分（去掉帧索引列）
            return view_features[0][:, 1:]

        # 帧同步
        common_frames = self.frame_synchronizer.find_common_frames(view_features)
        synced_features = self.frame_synchronizer.sync_features(view_features, common_frames)

        # 特征融合
        if self.fusion_method == "mean":
            # 按帧求平均
            fused = np.mean(synced_features, axis=0)
        elif self.fusion_method == "concat":
            # 特征拼接（需要调整输出维度）
            fused = np.concatenate(synced_features, axis=1)
        elif self.fusion_method == "max":
            # 按元素取最大值
            fused = np.maximum.reduce(synced_features)
        else:
            raise ValueError(f"Unsupported fusion method: {self.fusion_method}")

        return fused
    
    def process_task_views(self, task_dir: Path) -> Tuple[np.ndarray, List[int]]:
        """处理单个任务的所有视角数据"""
        # 查找所有txt文件
        txt_files = list(task_dir.glob("*.txt"))
        if not txt_files:
            return np.array([]), []
        
        # 按视角排序
        sorted_files = self.view_identifier.sort_by_view([str(f) for f in txt_files])
        
        # 加载特征数据
        view_features = []
        for file_path in sorted_files:
            data = np.loadtxt(file_path)
            if data.ndim == 1:
                data = data.reshape(1, -1)
            view_features.append(data)
        
        # 融合特征
        fused_features = self.fuse_views(view_features)
        
        # 提取帧索引
        if fused_features.size > 0:
            frame_indices = self.frame_synchronizer.find_common_frames(view_features)
        else:
            frame_indices = []
        
        return fused_features, frame_indices

class LabelMapper:
    """动态标签映射器，确保标签的一致性"""
    
    def __init__(self, label_map_path: str):
        self.label_map_path = Path(label_map_path)
        self.action_to_id = {}
        self.id_to_action = {}
        self.next_id = 0
        
        # 尝试加载现有映射
        self._load_existing_map()
    
    def _load_existing_map(self):
        """加载现有的标签映射"""
        if self.label_map_path.exists():
            try:
                with open(self.label_map_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.id_to_action = {int(k): v for k, v in data.items()}
                self.action_to_id = {v: int(k) for k, v in data.items()}
                self.next_id = max(self.id_to_action.keys()) + 1 if self.id_to_action else 0
                
            except (json.JSONDecodeError, ValueError) as e:
                print(f"Warning: Failed to load label map from {self.label_map_path}: {e}")
                self._initialize_default_map()
        else:
            self._initialize_default_map()
    
    def _initialize_default_map(self):
        """初始化默认标签映射"""
        default_actions = ["SIL", "take_bowl", "pour_cereals", "pour_milk", "stir_cereals"]
        for i, action in enumerate(default_actions):
            self.action_to_id[action] = i
            self.id_to_action[i] = action
        self.next_id = len(default_actions)
    
    def get_or_create_id(self, action_name: str) -> int:
        """获取或创建动作的ID"""
        if action_name in self.action_to_id:
            return self.action_to_id[action_name]
        
        # 创建新的ID
        new_id = self.next_id
        self.action_to_id[action_name] = new_id
        self.id_to_action[new_id] = action_name
        self.next_id += 1
        
        return new_id
    
    def save_map(self):
        """保存标签映射到文件"""
        self.label_map_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.label_map_path, 'w', encoding='utf-8') as f:
            json.dump(self.id_to_action, f, ensure_ascii=False, indent=2)
    
    def parse_segment_line(self, line: str) -> Tuple[int, int, int]:
        """解析段级标签行，返回(start_frame, end_frame, action_id)"""
        parts = line.strip().split()
        if len(parts) < 2:
            raise ValueError(f"Invalid segment line: {line}")
        
        # 解析帧范围
        frame_range = parts[0]
        if '-' in frame_range:
            start_frame, end_frame = map(int, frame_range.split('-'))
        else:
            start_frame = end_frame = int(frame_range)
        
        # 解析动作名称
        action_name = ' '.join(parts[1:])
        action_id = self.get_or_create_id(action_name)
        
        return start_frame, end_frame, action_id

class DataValidator:
    """数据一致性验证器"""

    def __init__(self, tolerance: float = 1e-6):
        self.tolerance = tolerance

    def validate_conversion(self, txt_data: np.ndarray, npy_data: np.ndarray,
                          txt_path: str) -> Tuple[bool, List[str]]:
        """验证TXT到NPY转换的一致性"""
        errors = []

        # 检查是否有数据
        if txt_data.size == 0 or npy_data.size == 0:
            errors.append("Empty data: txt or npy is empty")
            return False, errors

        # 仅对融合后的特征数据进行验证，不检查形状（因为融合后形状会改变）
        # 融合后的npy_data应该是只包含特征的数组，没有帧索引列
        
        # 检查数值一致性
        try:
            # 如果txt_data有多列（帧索引+特征），排除第一列
            if txt_data.ndim == 2 and txt_data.shape[1] > 1:
                txt_features = txt_data[:, 1:]  # 排除帧索引列
            else:
                txt_features = txt_data
            
            # 比较特征维度
            if txt_features.shape[1] != 64 and npy_data.shape[1] != 64:
                errors.append(f"Feature dimension mismatch: expected 64, got txt:{txt_features.shape[1]}, npy:{npy_data.shape[1]}")
            
            # 跳过均值和方差检查，因为融合后的数据会有所不同
            # 只检查数据范围的合理性
            txt_min, txt_max = np.min(txt_features), np.max(txt_features)
            npy_min, npy_max = np.min(npy_data), np.max(npy_data)
            
            # 检查数据范围是否合理（避免inf或nan）
            if np.isnan(txt_min) or np.isnan(txt_max) or np.isinf(txt_min) or np.isinf(txt_max):
                errors.append("TXT data contains NaN or Inf values")
            if np.isnan(npy_min) or np.isnan(npy_max) or np.isinf(npy_min) or np.isinf(npy_max):
                errors.append("NPY data contains NaN or Inf values")
                
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")

        is_valid = len(errors) == 0
        return is_valid, errors

    def validate_label_consistency(self, label_data: np.ndarray,
                                 expected_frames: List[int]) -> Tuple[bool, List[str]]:
        """验证标签数据的一致性"""
        errors = []

        if len(label_data) != len(expected_frames):
            errors.append(f"Label length {len(label_data)} != frame count {len(expected_frames)}")

        # 检查标签值的合理性
        unique_labels = np.unique(label_data)
        if np.any(unique_labels < 0):
            errors.append("Found negative label values")

        is_valid = len(errors) == 0
        return is_valid, errors

class BreakfastDataLoader:
    """Breakfast数据集加载器 - 完整实现"""

    def __init__(self, config: Dict):
        self.config = config
        self.label_mapper = LabelMapper(config['data']['label_map_path'])
        self.multi_view_fusion = MultiViewFusion()
        self.data_validator = DataValidator()

        # 数据路径
        self.raw_data_path = Path(config['data']['raw_data_path'])
        self.raw_label_path = Path(config['data']['raw_label_path'])
        self.npy_data_path = Path(config['data']['npy_data_path'])
        self.npy_label_path = Path(config['data']['npy_label_path'])

        # 训练/测试划分
        self.train_splits = config['training']['train_splits']
        self.test_splits = config['training']['test_splits']

    @property
    def num_classes(self) -> int:
        """获取动作类别数量"""
        return len(self.label_mapper.action_to_id)

    def load_npy_data(self, split: str, task: str) -> Tuple[np.ndarray, np.ndarray]:
        """加载并融合多视角数据"""
        # 查找任务目录
        task_dir = self.npy_data_path / split / task
        if not task_dir.exists():
            raise FileNotFoundError(f"Task directory not found: {task_dir}")

        # 收集所有视角的特征文件（按任务名过滤）
        view_files = sorted([f for f in task_dir.glob("*.npy") if task in f.name])
        if not view_files:
            raise FileNotFoundError(f"No npy files found for task {task} in {task_dir}")

        # 加载所有视角
        view_features = []
        for view_file in view_files:
            features = np.load(view_file)
            view_features.append(features)

        # 多视角融合
        if len(view_features) == 1:
            fused_features = view_features[0][:, 1:]  # 去掉帧索引
        else:
            # 使用MultiViewFusion进行融合
            fused_features = self.multi_view_fusion.fuse_views(view_features)

        # 加载对应的标签
        label_path = self.npy_label_path / f"{split}_label" / task
        # 找到任意一个标签文件（所有视角共享相同标签）
        label_files = list(label_path.glob(f"*{task}*.npy"))
        if not label_files:
            raise FileNotFoundError(f"No label files found for task {task} in {label_path}")

        labels = np.load(label_files[0])

        # 确保特征和标签长度匹配
        min_len = min(len(fused_features), len(labels))
        return fused_features[:min_len], labels[:min_len]

    def get_transition_samples(self, features: np.ndarray, labels: np.ndarray) -> List[Dict]:
        """生成动作转换样本（仅在段边界处）"""
        if len(features) == 0 or len(labels) == 0:
            return []

        samples = []

        # 找到动作转换点
        for i in range(len(labels) - 1):
            current_action = labels[i]
            next_action = labels[i + 1]

            # 只在动作发生变化时创建样本
            if current_action != next_action:
                sample = {
                    'features': features[i],  # 当前段的最后一帧特征
                    'current_action': int(current_action),
                    'next_action': int(next_action),
                    'frame_index': i
                }
                samples.append(sample)

        return samples

    def get_all_training_data(self) -> List[Dict]:
        """获取所有训练数据的转换样本"""
        all_samples = []

        for split in self.train_splits:
            split_path = self.npy_data_path / split
            if not split_path.exists():
                continue

            # 遍历所有任务目录
            for task_dir in split_path.iterdir():
                if not task_dir.is_dir():
                    continue

                task_name = task_dir.name

                try:
                    features, labels = self.load_npy_data(split, task_name)
                    samples = self.get_transition_samples(features, labels)

                    # 添加元数据
                    for sample in samples:
                        sample['split'] = split
                        sample['task'] = task_name

                    all_samples.extend(samples)

                except Exception as e:
                    print(f"Warning: Failed to load {split}/{task_name}: {e}")

        return all_samples

    def get_all_test_data(self) -> Dict[str, Tuple[np.ndarray, np.ndarray]]:
        """获取所有测试数据"""
        test_data = {}

        for split in self.test_splits:
            split_path = self.npy_data_path / split
            if not split_path.exists():
                continue

            # 遍历所有任务目录
            for task_dir in split_path.iterdir():
                if not task_dir.is_dir():
                    continue

                task_name = task_dir.name

                try:
                    features, labels = self.load_npy_data(split, task_name)
                    test_data[f"{split}_{task_name}"] = (features, labels)

                except Exception as e:
                    print(f"Warning: Failed to load test data {split}/{task_name}: {e}")

        return test_data
