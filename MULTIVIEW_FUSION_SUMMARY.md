# 多视角融合数据加载器实现总结

## 概述

成功修改了 `src/utils/data_loader.py` 中的 `BreakfastDataLoader` 类，实现了多视角融合功能。该实现完全符合 IDEA.md 中第1.2章节的要求，支持在数据加载时动态融合多个视角的特征数据。

## 核心功能

### 1. 多视角数据加载 (`load_npy_data` 方法)

```python
def load_npy_data(self, split: str, task: str) -> Tuple[np.ndarray, np.ndarray]:
    """加载并融合多视角数据"""
    # 查找任务目录
    task_dir = self.npy_data_path / split / task
    
    # 收集所有视角的特征文件
    view_files = sorted([f for f in task_dir.glob("*.npy") if task in f.name])
    
    # 加载所有视角并进行融合
    # 多视角融合
    if len(view_features) == 1:
        fused_features = view_features[0][:, 1:]  # 去掉帧索引
    else:
        # 使用MultiViewFusion进行融合
        fused_features = self.multi_view_fusion.fuse_views(view_features)
    
    # 加载标签并确保长度匹配
    return fused_features[:min_len], labels[:min_len]
```

### 2. 视角识别与排序

- **ViewIdentifier 类**: 自动识别不同的视角命名模式
  - `webcam01`, `webcam02`: 网络摄像头视角
  - `cam01`: 普通摄像头视角  
  - `stereo01`: 立体视角（分配独立ID范围）
- **自动排序**: 按视角ID排序确保一致性

### 3. 帧同步机制

- **FrameSynchronizer 类**: 处理不同视角间的帧数差异
  - 找出所有视角共同存在的帧索引
  - 对缺失帧使用零向量填充
  - 确保时间维度对齐

### 4. 多种融合策略

- **MEAN 融合** (默认): 按帧求平均，输出64维特征
- **MAX 融合**: 按元素取最大值，输出64维特征
- **CONCAT 融合**: 特征拼接，输出维度为 64 × 视角数

## 测试结果

### 基本功能测试
- ✅ 成功加载并融合27个视角的cereals任务数据
- ✅ 帧同步正确工作，找到380个共同帧
- ✅ 最终输出64维特征，符合IDEA.md要求
- ✅ 数据质量检查通过（无NaN/Inf值）

### 融合方法测试
- ✅ MEAN融合: (380, 64) 特征范围 [-8.254, 6.145]
- ✅ MAX融合: (380, 64) 特征范围 [0.000, 23.224]  
- ✅ CONCAT融合: (380, 1728) 特征范围 [-27.378, 23.224]

### 批量加载测试
- ✅ 训练数据: 93个转换样本 (s1:36, s2:30, s3:27)
- ✅ 测试数据: 10个任务，总计9,952帧

## 使用示例

### 基本使用
```python
from utils.data_loader import BreakfastDataLoader

# 创建数据加载器（默认mean融合）
data_loader = BreakfastDataLoader(config)

# 加载融合后的数据
features, labels = data_loader.load_npy_data("s1", "cereals")
print(f"特征形状: {features.shape}")  # (380, 64)
```

### 切换融合方法
```python
from utils.data_loader import MultiViewFusion

# 使用MAX融合
data_loader.multi_view_fusion = MultiViewFusion(fusion_method="max")
features, labels = data_loader.load_npy_data("s1", "cereals")

# 使用CONCAT融合（维度会增加）
data_loader.multi_view_fusion = MultiViewFusion(fusion_method="concat")
features, labels = data_loader.load_npy_data("s1", "cereals")
print(f"拼接后维度: {features.shape[1]}")  # 1728 = 64 × 27
```

### 批量加载
```python
# 获取所有训练转换样本
all_samples = data_loader.get_all_training_data()
print(f"总样本数: {len(all_samples)}")

# 获取所有测试数据
test_data = data_loader.get_all_test_data()
```

## 设计优势

1. **按需融合**: 只在数据加载时进行融合，保持原始数据完整性
2. **灵活配置**: 支持多种融合策略，可根据实验需要切换
3. **自动同步**: 智能处理不同视角间的帧数差异
4. **错误处理**: 完善的异常处理和数据验证机制
5. **扩展性**: 支持自定义融合方法（如加权融合）

## 符合IDEA.md要求

- ✅ **1.2节 视角融合策略**: 实现了on-the-fly融合
- ✅ **任务识别**: 自动识别任务的所有视角文件
- ✅ **帧同步**: 保留所有视角共同存在的帧
- ✅ **特征平均**: 默认使用逐元素平均融合
- ✅ **零向量填充**: 对缺失帧进行填充处理

## 文件修改记录

- `src/utils/data_loader.py`: 
  - 修改 `load_npy_data` 方法支持多视角融合
  - 改进 `ViewIdentifier` 类的视角识别逻辑
  - 优化 `MultiViewFusion.fuse_views` 方法
  - 修复 `get_all_training_data` 和 `get_all_test_data` 方法

## 后续建议

1. 可考虑添加更多融合策略（如注意力机制融合）
2. 可优化大规模数据的加载性能（如缓存机制）
3. 可添加融合质量评估指标
4. 可支持动态权重调整的融合方法
