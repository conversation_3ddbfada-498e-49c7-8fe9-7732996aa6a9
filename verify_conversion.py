#!/usr/bin/env python3
"""
验证TXT到NPY转换的正确性
检查数值误差和行数一致性
"""

import numpy as np
import sys
from pathlib import Path
import random


def verify_single_file(txt_path: Path, npy_path: Path, tolerance: float = 1e-6) -> tuple:
    """验证单个文件的转换正确性"""
    errors = []
    
    try:
        # 加载文件
        txt_data = np.loadtxt(txt_path)
        if txt_data.ndim == 1:
            txt_data = txt_data.reshape(1, -1)
        
        npy_data = np.load(npy_path)
        
        # 检查行数
        if txt_data.shape[0] != npy_data.shape[0]:
            errors.append(f"Row count mismatch: TXT {txt_data.shape[0]} vs NPY {npy_data.shape[0]}")
        
        # 检查列数
        if txt_data.shape[1] != npy_data.shape[1]:
            errors.append(f"Column count mismatch: TXT {txt_data.shape[1]} vs NPY {npy_data.shape[1]}")
        
        # 检查帧索引（第一列）
        if txt_data.shape[0] == npy_data.shape[0]:
            frame_idx_match = (txt_data[:, 0].astype(np.int32) == npy_data[:, 0]).all()
            if not frame_idx_match:
                max_diff = np.max(np.abs(txt_data[:, 0] - npy_data[:, 0]))
                errors.append(f"Frame index mismatch: max diff = {max_diff}")
        
        # 检查特征值（其他列）
        if txt_data.shape == npy_data.shape and txt_data.shape[1] > 1:
            feature_diff = np.abs(txt_data[:, 1:] - npy_data[:, 1:])
            max_diff = np.max(feature_diff)
            if max_diff > tolerance:
                errors.append(f"Feature value mismatch: max diff = {max_diff} > {tolerance}")
        
        return len(errors) == 0, errors
        
    except Exception as e:
        return False, [f"Error processing files: {str(e)}"]


def verify_all_conversions(sample_size: int = None):
    """验证所有转换的文件"""
    txt_base = Path("/data2/syd_data/Breakfast_Data/breakfast_data")
    npy_base = Path("/data2/syd_data/Breakfast_Data/breakfast_data_npy")
    
    # 收集所有txt文件
    all_txt_files = []
    for split in ['s1', 's2', 's3', 's4']:
        split_dir = txt_base / split
        if split_dir.exists():
            all_txt_files.extend(list(split_dir.rglob("*.txt")))
    
    # 如果指定了样本大小，随机抽样
    if sample_size and sample_size < len(all_txt_files):
        all_txt_files = random.sample(all_txt_files, sample_size)
    
    print(f"Verifying {len(all_txt_files)} files...")
    
    total_files = 0
    passed_files = 0
    failed_files = []
    
    for txt_path in all_txt_files:
        # 构建对应的npy路径
        relative_path = txt_path.relative_to(txt_base)
        npy_path = npy_base / relative_path.with_suffix('.npy')
        
        if not npy_path.exists():
            failed_files.append((str(txt_path), ["NPY file not found"]))
            continue
        
        total_files += 1
        is_valid, errors = verify_single_file(txt_path, npy_path)
        
        if is_valid:
            passed_files += 1
        else:
            failed_files.append((str(txt_path), errors))
    
    # 输出结果
    print(f"\nVerification Results:")
    print(f"  Total files checked: {total_files}")
    print(f"  Passed: {passed_files}")
    print(f"  Failed: {len(failed_files)}")
    
    if failed_files:
        print(f"\nFailed files:")
        for file_path, errors in failed_files[:10]:  # 只显示前10个
            print(f"\n  {file_path}:")
            for error in errors:
                print(f"    - {error}")
        
        if len(failed_files) > 10:
            print(f"\n  ... and {len(failed_files) - 10} more")
    
    return len(failed_files) == 0


if __name__ == "__main__":
    # 可以指定样本大小进行快速验证
    sample_size = None
    if len(sys.argv) > 1:
        sample_size = int(sys.argv[1])
    
    success = verify_all_conversions(sample_size)
    sys.exit(0 if success else 1) 