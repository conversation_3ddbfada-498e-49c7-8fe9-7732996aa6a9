#!/usr/bin/env python3
"""多视角融合数据加载器使用示例"""

import sys
import numpy as np
import yaml
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

from utils.data_loader import BreakfastDataLoader, MultiViewFusion

def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("基本使用示例")
    print("=" * 60)
    
    # 加载配置
    with open('configs/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建数据加载器（默认使用mean融合）
    data_loader = BreakfastDataLoader(config)
    
    # 加载训练数据
    print("加载训练数据...")
    features, labels = data_loader.load_npy_data("s1", "cereals")
    
    print(f"特征形状: {features.shape}")
    print(f"标签形状: {labels.shape}")
    print(f"特征维度: {features.shape[1]} (64维)")
    print(f"数据类型: {features.dtype}")
    
    # 获取转换样本（用于训练）
    print("\n获取动作转换样本...")
    samples = data_loader.get_transition_samples(features, labels)
    print(f"转换样本数量: {len(samples)}")
    
    if samples:
        sample = samples[0]
        print(f"样本示例:")
        print(f"  当前动作: {sample['current_action']}")
        print(f"  下一动作: {sample['next_action']}")
        print(f"  特征形状: {sample['features'].shape}")

def example_different_fusion_methods():
    """不同融合方法示例"""
    print("\n" + "=" * 60)
    print("不同融合方法示例")
    print("=" * 60)
    
    # 加载配置
    with open('configs/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    fusion_methods = ["mean", "max", "concat"]
    
    for method in fusion_methods:
        print(f"\n{method.upper()} 融合方法:")
        print("-" * 30)
        
        # 创建使用特定融合方法的数据加载器
        data_loader = BreakfastDataLoader(config)
        data_loader.multi_view_fusion = MultiViewFusion(fusion_method=method)
        
        try:
            features, labels = data_loader.load_npy_data("s1", "cereals")
            print(f"  特征形状: {features.shape}")
            print(f"  特征范围: [{features.min():.3f}, {features.max():.3f}]")
            
            if method == "concat":
                expected_dim = 64 * 27  # 假设有27个视角
                print(f"  期望维度: {expected_dim} (64 × 视角数)")
                
        except Exception as e:
            print(f"  错误: {e}")

def example_batch_loading():
    """批量加载示例"""
    print("\n" + "=" * 60)
    print("批量加载示例")
    print("=" * 60)
    
    # 加载配置
    with open('configs/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    data_loader = BreakfastDataLoader(config)
    
    # 获取所有训练数据
    print("获取所有训练数据...")
    all_samples = data_loader.get_all_training_data()
    
    print(f"总训练样本数: {len(all_samples)}")
    
    # 统计每个split的样本数
    split_counts = {}
    for sample in all_samples:
        split = sample['split']
        split_counts[split] = split_counts.get(split, 0) + 1
    
    print("各split的样本分布:")
    for split, count in split_counts.items():
        print(f"  {split}: {count} 样本")
    
    # 获取测试数据
    print("\n获取测试数据...")
    test_data = data_loader.get_all_test_data()
    
    print(f"测试数据集数量: {len(test_data)}")
    for key, (features, labels) in test_data.items():
        print(f"  {key}: 特征{features.shape}, 标签{labels.shape}")

def example_custom_fusion():
    """自定义融合示例"""
    print("\n" + "=" * 60)
    print("自定义融合示例")
    print("=" * 60)
    
    # 创建自定义融合器
    class WeightedFusion(MultiViewFusion):
        """加权融合示例"""
        
        def __init__(self, weights=None):
            super().__init__(fusion_method="mean")
            self.weights = weights
        
        def fuse_views(self, view_features):
            """加权融合多个视角"""
            if not view_features:
                return np.array([])
            
            if len(view_features) == 1:
                return view_features[0][:, 1:]  # 去掉帧索引
            
            # 帧同步
            common_frames = self.frame_synchronizer.find_common_frames(view_features)
            synced_features = self.frame_synchronizer.sync_features(view_features, common_frames)
            
            # 加权平均
            if self.weights is None:
                # 默认等权重
                weights = np.ones(len(synced_features)) / len(synced_features)
            else:
                weights = np.array(self.weights)
                weights = weights / weights.sum()  # 归一化
            
            # 计算加权平均
            fused = np.zeros_like(synced_features[0])
            for i, features in enumerate(synced_features):
                fused += weights[i] * features
            
            return fused
    
    # 使用自定义融合器
    print("使用自定义加权融合...")
    
    # 加载配置
    with open('configs/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    data_loader = BreakfastDataLoader(config)
    
    # 设置权重（前几个视角权重更高）
    num_views = 27  # 假设有27个视角
    weights = np.exp(-np.arange(num_views) * 0.1)  # 指数衰减权重
    
    data_loader.multi_view_fusion = WeightedFusion(weights=weights)
    
    try:
        features, labels = data_loader.load_npy_data("s1", "cereals")
        print(f"  加权融合结果形状: {features.shape}")
        print(f"  特征范围: [{features.min():.3f}, {features.max():.3f}]")
        print("  ✓ 自定义融合成功")
        
    except Exception as e:
        print(f"  ✗ 自定义融合失败: {e}")

if __name__ == "__main__":
    example_basic_usage()
    example_different_fusion_methods()
    example_batch_loading()
    example_custom_fusion()
