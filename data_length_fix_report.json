{"timestamp": "2025-06-18T17:17:51.310348", "fix_stats": {"total_files": 3424, "mismatched_files": 1712, "fixed_files": 40, "length_distribution": {}, "mismatch_details": [], "backup_created": true, "backup_path": "/data2/syd_data/Breakfast_Data/backup_20250618_171722"}, "length_analysis": {"total_tasks": 40, "mismatched_tasks": 40, "mismatch_details": [{"task": "s1/sandwich", "feature_lengths": [3041, 3041, 3042, 3041, 1446, 1447, 1446, 1446, 2741, 2504, 2136, 2136, 672, 1628, 1846, 1778, 1778, 1725, 1725, 1755, 1756, 1471, 1471, 1471, 1045, 1045, 1045], "label_lengths": [1759, 1782, 1049, 1450, 1729, 3045, 1850, 1759, 1782, 3045, 1450, 3045, 1450, 3045, 2140, 1475, 676, 1049, 1450, 1729, 1475, 1475, 1632, 2745, 2508, 1049, 2140], "max_feature": 3042, "max_label": 3045, "recommended_length": 3045}, {"task": "s1/salat", "feature_lengths": [4450, 4450, 4450, 2778, 2778, 3844, 3244, 3323, 3326, 2986, 2673, 4850, 2709, 2709, 2147, 2147, 2147, 2361, 2361, 3418, 3418, 3418, 3917, 3917], "label_lengths": [4454, 2713, 2677, 4454, 2782, 3921, 2151, 4854, 2151, 2365, 4454, 3848, 3422, 3330, 3422, 2782, 2713, 3921, 2990, 3330, 3422, 2365, 2151, 3248], "max_feature": 4850, "max_label": 4854, "recommended_length": 4854}, {"task": "s1/pancake", "feature_lengths": [6693, 6693, 6693, 6693, 5893, 4985, 3909, 3915, 5260, 4577, 6693, 6693, 2871, 2871, 4437, 4437, 4437, 5927, 5927, 7458, 7458, 7458], "label_lengths": [4581, 4441, 2875, 5931, 5897, 3919, 5264, 3913, 6697, 6697, 7462, 4441, 4989, 6697, 4441, 5931, 7462, 6697, 2875, 6697, 7462, 6697], "max_feature": 7458, "max_label": 7462, "recommended_length": 7462}, {"task": "s1/scrambledegg", "feature_lengths": [2719, 2721, 2726, 2726, 2697, 1350, 1350, 2269, 2269, 2269, 1732, 1284, 1284, 2650, 2650, 2779, 2779, 1696, 1696, 1696, 2853, 2853, 2853, 1762, 1762, 1763], "label_lengths": [1766, 2273, 2730, 2783, 1354, 2730, 1700, 1766, 1288, 2654, 2273, 2857, 2730, 1700, 2730, 1288, 1736, 2783, 2701, 1766, 1700, 2857, 2273, 2857, 1354, 2654], "max_feature": 2853, "max_label": 2857, "recommended_length": 2857}, {"task": "s1/tea", "feature_lengths": [1311, 1311, 1311, 1311, 397, 397, 396, 397, 1662, 512, 661, 661, 367, 367, 423, 422, 842, 332, 332, 262, 262, 442, 442, 442, 398, 398, 398, 355, 355, 355], "label_lengths": [402, 846, 446, 359, 426, 266, 1315, 401, 1315, 359, 402, 371, 1666, 446, 371, 1315, 359, 336, 402, 426, 665, 401, 401, 266, 665, 1315, 446, 401, 336, 516], "max_feature": 1662, "max_label": 1666, "recommended_length": 1666}, {"task": "s1/juice", "feature_lengths": [2792, 2792, 1315, 1199, 1199, 1246, 1248, 1172, 1172, 2387, 1604, 1604, 1604, 1332, 1332, 1332], "label_lengths": [1336, 1203, 1176, 1176, 1203, 1336, 1253, 1608, 1319, 1336, 2391, 1253, 2796, 1608, 1608, 2796], "max_feature": 2792, "max_label": 2796, "recommended_length": 2796}, {"task": "s1/coffee", "feature_lengths": [918, 918, 918, 918, 1120, 473, 272, 572, 572, 1209, 373, 373, 216, 216, 586, 586, 366, 366, 366, 694, 694, 694], "label_lengths": [220, 377, 576, 922, 220, 370, 377, 698, 698, 922, 1213, 477, 590, 922, 290, 698, 576, 590, 1124, 370, 922, 370], "max_feature": 1209, "max_label": 1213, "recommended_length": 1213}, {"task": "s1/cereals", "feature_lengths": [833, 833, 833, 834, 482, 482, 480, 482, 1183, 498, 938, 1104, 754, 829, 536, 536, 647, 647, 676, 676, 677, 723, 723, 723, 380, 380, 380], "label_lengths": [384, 680, 502, 651, 651, 680, 942, 384, 833, 486, 837, 837, 384, 486, 540, 837, 486, 727, 540, 727, 727, 1187, 486, 1110, 680, 837, 758], "max_feature": 1183, "max_label": 1187, "recommended_length": 1187}, {"task": "s1/friedegg", "feature_lengths": [4267, 4267, 4267, 3358, 3358, 3353, 3358, 3408, 2206, 2988, 2988, 4107, 2386, 3550, 8344, 8344, 4164, 4164, 4164, 2738, 2738, 2872, 2872, 2872, 2362, 2362, 2362], "label_lengths": [2992, 2876, 2366, 4168, 2366, 8348, 3362, 3362, 3554, 4271, 4111, 4168, 2366, 8348, 3362, 4271, 2876, 3362, 2992, 4271, 4168, 2742, 2742, 2876, 3412, 2390, 2210], "max_feature": 8344, "max_label": 8348, "recommended_length": 8348}, {"task": "s1/milk", "feature_lengths": [1159, 1158, 1158, 1158, 746, 754, 748, 735, 1081, 584, 1005, 1005, 1005, 1338, 742, 742, 984, 776, 776, 776, 801, 801, 575, 576, 575, 793, 793, 794, 704, 704, 704], "label_lengths": [579, 797, 805, 1009, 1162, 1162, 758, 579, 708, 805, 746, 780, 780, 988, 797, 797, 758, 746, 1009, 708, 579, 708, 1162, 1085, 758, 588, 758, 780, 1009, 1162, 1342], "max_feature": 1338, "max_label": 1342, "recommended_length": 1342}, {"task": "s2/sandwich", "feature_lengths": [1938, 1938, 1938, 1938, 756, 773, 774, 773, 707, 716, 716, 716, 1104, 1104, 1104, 1696, 1695, 1695, 1695, 1489, 1489, 1489, 1484, 1485, 1485, 1484, 1484, 2242, 2242, 2242, 2242, 2242, 1914, 1914, 1914, 1914, 1866, 1866, 1866, 1818, 1819, 1819, 2773, 2773], "label_lengths": [1942, 777, 1493, 720, 1699, 777, 1488, 1108, 1488, 1870, 1942, 1918, 777, 1918, 1108, 1699, 1493, 2246, 1823, 1108, 2246, 2777, 1870, 720, 2246, 760, 1942, 1493, 720, 1699, 1488, 720, 2246, 1823, 1918, 1918, 1823, 2777, 1942, 1488, 1699, 1488, 1870, 2246], "max_feature": 2773, "max_label": 2777, "recommended_length": 2777}, {"task": "s2/salat", "feature_lengths": [4137, 4137, 4137, 4137, 3790, 3790, 3790, 3790, 3931, 3932, 1431, 1431, 1431, 1526, 1526, 1526, 3689, 3689, 3689, 3379, 3379, 3379, 3137, 3137, 3137, 3022, 3022, 3022, 3022, 3022, 2717, 2717, 2717, 2717, 3677, 3682, 3682, 4881, 4881, 4881, 4272, 4272, 4272], "label_lengths": [3794, 2721, 4141, 3141, 3141, 4885, 3383, 3693, 3026, 3686, 4141, 2721, 4885, 3693, 4141, 4885, 4276, 3794, 3383, 3686, 4141, 4276, 3383, 3026, 3794, 1435, 3141, 2721, 4276, 3693, 3794, 3935, 1435, 2721, 3935, 1435, 3686, 1530, 3026, 1530, 1530, 3026, 3026], "max_feature": 4881, "max_label": 4885, "recommended_length": 4885}, {"task": "s2/pancake", "feature_lengths": [8041, 8041, 8041, 8041, 6000, 6000, 6000, 7783, 7805, 7805, 3953, 3953, 3953, 5834, 5834, 5834, 5842, 5842, 5842, 4792, 4792, 4792, 4550, 4550, 4550, 4550, 8397, 8397, 8397, 8391, 5221, 5221, 6978, 6978, 6978, 6978, 6851, 6851, 6851, 6851], "label_lengths": [6982, 4796, 8045, 4796, 5225, 6982, 3957, 6004, 5838, 4554, 6982, 6855, 8401, 3957, 5846, 4554, 8045, 5846, 8401, 5846, 6004, 7809, 4554, 7809, 8045, 6004, 7809, 8045, 8401, 4796, 5838, 4554, 5225, 6855, 6855, 6855, 5838, 8401, 3957, 6982], "max_feature": 8397, "max_label": 8401, "recommended_length": 8401}, {"task": "s2/scrambledegg", "feature_lengths": [5192, 5192, 5192, 7911, 7911, 3595, 3595, 3595, 1207, 1207, 3899, 3899, 3899, 3899, 5282, 5282, 5282, 2292, 2292, 2292, 2292, 2450, 2450, 2451, 4713, 4713, 4713, 2557, 2557, 2557, 3397, 3397, 3397, 3562, 3561, 3561, 3561], "label_lengths": [3903, 5286, 3401, 2561, 2561, 3903, 2296, 1211, 3599, 5196, 4717, 1211, 2296, 2296, 5286, 3565, 3401, 3565, 3565, 2296, 2454, 5196, 4717, 3599, 3599, 3565, 5286, 2454, 3903, 7915, 2561, 3401, 2454, 5196, 3903, 7915, 4717], "max_feature": 7911, "max_label": 7915, "recommended_length": 7915}, {"task": "s2/tea", "feature_lengths": [363, 363, 363, 363, 701, 701, 699, 704, 465, 465, 465, 465, 397, 397, 397, 397, 531, 531, 531, 531, 531, 833, 833, 833, 833, 833, 445, 445, 445, 445, 445, 714, 714, 714, 714, 714, 735, 735, 735, 1166, 1166, 1166, 1166, 896, 944, 944, 944], "label_lengths": [739, 535, 401, 739, 739, 837, 469, 900, 837, 367, 718, 449, 469, 837, 367, 948, 367, 469, 449, 401, 449, 367, 1170, 449, 837, 708, 401, 449, 708, 718, 718, 535, 1170, 837, 469, 948, 718, 535, 535, 948, 401, 1170, 535, 708, 1170, 708, 718], "max_feature": 1166, "max_label": 1170, "recommended_length": 1170}, {"task": "s2/juice", "feature_lengths": [2239, 2239, 2239, 2239, 1355, 1354, 1354, 1354, 1533, 1533, 1533, 1069, 1069, 1069, 913, 914, 913, 914, 1368, 1368, 1368, 1203, 1203, 1203, 1203, 1708, 1709, 1709, 1708, 1708, 1529, 1528, 1528, 1528, 602, 602, 602, 602, 1306, 1307, 1307, 1307, 2261, 2261, 2261, 2137, 2137], "label_lengths": [606, 1073, 1532, 1207, 606, 1358, 2243, 1712, 1073, 917, 1712, 1532, 2243, 1537, 917, 1532, 1712, 1372, 917, 1537, 1358, 2141, 1358, 2243, 1372, 606, 1311, 1537, 917, 1073, 1207, 1311, 2265, 606, 1712, 2243, 2141, 1311, 1532, 2265, 1712, 1207, 1372, 2265, 1207, 1311, 1358], "max_feature": 2261, "max_label": 2265, "recommended_length": 2265}, {"task": "s2/coffee", "feature_lengths": [572, 571, 360, 360, 360, 360, 345, 345, 345, 345, 959, 960, 690, 690, 690, 690, 690, 300, 300, 298, 300, 300, 296, 296, 296, 296, 296, 805, 805, 805, 805, 806, 449, 449, 449, 449, 449, 178, 183, 183, 518, 518, 508, 519, 902, 902, 902, 902], "label_lengths": [906, 187, 694, 964, 694, 809, 694, 300, 300, 300, 349, 304, 364, 304, 453, 809, 304, 906, 522, 809, 187, 364, 349, 453, 522, 349, 364, 575, 304, 522, 809, 964, 453, 906, 300, 349, 809, 906, 187, 694, 522, 575, 364, 304, 453, 453, 300, 694], "max_feature": 960, "max_label": 964, "recommended_length": 964}, {"task": "s2/cereals", "feature_lengths": [545, 546, 545, 545, 622, 620, 621, 621, 462, 461, 462, 462, 474, 475, 476, 475, 547, 547, 548, 547, 1271, 1272, 1271, 1271, 898, 898, 898, 898, 898, 1039, 1040, 1040, 1039, 1039, 904, 904, 904, 904, 793, 792, 790, 657, 656, 656, 657, 983, 983, 983, 385, 385, 385], "label_lengths": [625, 551, 660, 466, 987, 908, 551, 479, 1043, 1043, 796, 389, 389, 479, 908, 908, 549, 660, 987, 1275, 551, 479, 902, 1043, 479, 466, 551, 902, 549, 902, 908, 1043, 660, 1275, 1275, 902, 466, 549, 625, 902, 466, 625, 1043, 660, 796, 389, 1275, 987, 625, 549, 796], "max_feature": 1272, "max_label": 1275, "recommended_length": 1275}, {"task": "s2/friedegg", "feature_lengths": [6933, 6933, 6933, 6933, 2901, 2901, 2901, 2901, 3575, 3575, 3575, 3873, 3873, 3873, 3515, 3515, 3515, 1981, 1981, 1981, 1981, 1961, 2812, 2812, 2812, 2812, 2512, 2513, 2512, 2512, 3299, 3299, 3299, 3299, 3296, 2392, 2391, 2378, 3146, 3146, 2809, 2809, 2808, 2808], "label_lengths": [2516, 2816, 2516, 2816, 2813, 2816, 3303, 3579, 2516, 2395, 2816, 2905, 1985, 3877, 2905, 2905, 1985, 3519, 6937, 2516, 3303, 6937, 3519, 3303, 3150, 3579, 6937, 3519, 3303, 1985, 2395, 1985, 3579, 3303, 3877, 2905, 3150, 1985, 2813, 2813, 6937, 2395, 3877, 2813], "max_feature": 6933, "max_label": 6937, "recommended_length": 6937}, {"task": "s2/milk", "feature_lengths": [885, 885, 885, 885, 1202, 1202, 1202, 1202, 801, 802, 802, 801, 1037, 1037, 1037, 1037, 1004, 1004, 1004, 1004, 1004, 870, 870, 870, 870, 870, 926, 926, 926, 926, 926, 1247, 1247, 1247, 1247, 1247, 1403, 1404, 1403, 1403, 1507, 1507, 1507, 1227, 1227, 1227, 965, 965, 965, 965], "label_lengths": [930, 1041, 1206, 1231, 1206, 1251, 1511, 1251, 1511, 1251, 1041, 969, 1041, 930, 1008, 1407, 889, 1041, 1008, 874, 1407, 874, 1008, 805, 1511, 1407, 1231, 874, 1008, 805, 969, 1008, 1231, 930, 874, 1206, 889, 1251, 969, 930, 874, 1407, 805, 805, 1206, 889, 1251, 930, 889, 969], "max_feature": 1507, "max_label": 1511, "recommended_length": 1511}, {"task": "s3/sandwich", "feature_lengths": [2153, 2153, 2153, 1624, 1624, 3555, 3555, 1087, 1088, 1087, 1532, 1534, 1534, 1059, 1095, 1092, 1075, 1065, 1074, 1569, 1569, 1569, 1569, 1289, 1289, 1289, 1289, 1106, 1106, 1106, 1106, 1521, 1521, 1521, 1521, 1521, 1444, 1444, 1444, 1444, 1444, 1153, 1153, 1154, 1153, 1153], "label_lengths": [1110, 1293, 1448, 1157, 1628, 1525, 1448, 1448, 1157, 1110, 1573, 1448, 1293, 1110, 1091, 1293, 1157, 1538, 1538, 1079, 1573, 1525, 2157, 1079, 1538, 1573, 1091, 1293, 1525, 1525, 1573, 1157, 1157, 1079, 1099, 2157, 1525, 3559, 1628, 1099, 3559, 2157, 1448, 1099, 1091, 1110], "max_feature": 3555, "max_label": 3559, "recommended_length": 3559}, {"task": "s3/salat", "feature_lengths": [2724, 2724, 1813, 1813, 5694, 5694, 3625, 3625, 4541, 4543, 2240, 2240, 1616, 1617, 1610, 5293, 5293, 5293, 5293, 3491, 3491, 3491, 3491, 2641, 2641, 2641, 2641, 2666, 2666, 2666, 2666, 2667, 2194, 2195, 2194, 2195, 3219, 3219, 3219, 3219, 3219], "label_lengths": [5297, 2670, 2645, 2645, 1620, 2198, 5297, 4547, 2728, 2670, 2244, 2198, 5297, 2670, 3495, 1620, 2244, 1817, 2645, 2198, 3223, 3495, 2645, 3495, 3495, 3629, 5698, 3223, 2670, 4547, 3223, 2198, 3629, 3223, 2670, 1817, 5698, 5297, 2728, 1620, 3223], "max_feature": 5694, "max_label": 5698, "recommended_length": 5698}, {"task": "s3/pancake", "feature_lengths": [7201, 7201, 7200, 6814, 6814, 9008, 9011, 6791, 6791, 3384, 3384, 3484, 3506, 7050, 7050, 7050, 7050, 4452, 4452, 4452, 4452, 7495, 7495, 7495, 7495, 8987, 8987, 8985, 8987, 8987, 3310, 3310, 3310, 3307, 4990, 4990, 4989, 4990, 4990], "label_lengths": [9015, 4994, 6795, 7499, 8991, 4994, 8991, 3510, 4994, 4994, 3314, 6818, 8991, 7054, 3510, 6795, 7205, 8991, 4994, 7499, 4456, 7499, 7499, 7205, 7054, 8991, 4456, 6818, 3388, 9015, 7054, 7054, 7205, 3314, 4456, 3388, 3314, 3314, 4456], "max_feature": 9011, "max_label": 9015, "recommended_length": 9015}, {"task": "s3/scrambledegg", "feature_lengths": [2943, 2943, 2943, 2846, 2846, 2846, 4674, 4674, 2849, 2849, 3341, 3329, 4287, 4452, 4452, 2079, 2085, 2085, 2244, 2244, 2244, 2244, 1362, 1362, 1362, 1362, 3450, 3450, 3450, 3450, 4404, 4404, 4404, 4404, 4404, 1857, 1857, 1857, 1857, 1857, 3019, 3019, 3019, 3020, 3019], "label_lengths": [4408, 4678, 4408, 2947, 3023, 1861, 4408, 2248, 2248, 3023, 2850, 2248, 1366, 1861, 2853, 4456, 1366, 2089, 4408, 3023, 3454, 2248, 3023, 2089, 4456, 4408, 3454, 2853, 1366, 3345, 3454, 4456, 1861, 2850, 3345, 2947, 2850, 1366, 1861, 3454, 2947, 1861, 4678, 3023, 2089], "max_feature": 4674, "max_label": 4678, "recommended_length": 4678}, {"task": "s3/tea", "feature_lengths": [639, 639, 639, 370, 370, 370, 1245, 1245, 1245, 490, 489, 489, 588, 588, 588, 808, 812, 808, 496, 496, 479, 496, 464, 463, 463, 463, 363, 364, 364, 364, 443, 443, 443, 443, 1301, 1300, 1291, 1288, 1108, 1108, 1108, 1108, 1236, 1236, 1236, 1236, 1236], "label_lengths": [493, 1112, 1305, 374, 500, 467, 374, 816, 592, 447, 1112, 592, 1305, 368, 1305, 1249, 1249, 592, 368, 643, 374, 493, 1240, 368, 816, 1305, 1240, 1240, 643, 500, 467, 1249, 493, 447, 816, 643, 1112, 368, 500, 500, 467, 447, 1240, 447, 1112, 1240, 467], "max_feature": 1301, "max_label": 1305, "recommended_length": 1305}, {"task": "s3/juice", "feature_lengths": [1398, 1398, 1167, 1155, 2408, 2408, 1278, 1279, 1039, 1049, 865, 936, 936, 1209, 1201, 1207, 1695, 1695, 1695, 1695, 1623, 1624, 1624, 1624, 1465, 1465, 1465, 1465, 1808, 1809, 1808, 999, 999, 999, 999, 999, 1580, 1580, 1580, 1579, 1579], "label_lengths": [1469, 1003, 1003, 1469, 1699, 1216, 1067, 1402, 1067, 1628, 1583, 1812, 1469, 940, 1628, 2412, 1583, 1402, 1583, 1699, 940, 1583, 1628, 1282, 1812, 1003, 1170, 1216, 940, 1628, 2412, 1003, 1282, 1469, 1699, 1699, 1812, 1170, 1583, 1216, 1003], "max_feature": 2408, "max_label": 2412, "recommended_length": 2412}, {"task": "s3/coffee", "feature_lengths": [587, 587, 587, 472, 472, 472, 684, 684, 685, 406, 406, 406, 620, 620, 492, 626, 447, 438, 446, 446, 1159, 1024, 1023, 1023, 1022, 763, 763, 763, 755, 641, 641, 418, 420, 420, 420, 874, 873, 873, 874, 873], "label_lengths": [450, 476, 645, 645, 688, 591, 591, 476, 1028, 688, 624, 624, 630, 450, 450, 877, 688, 767, 767, 424, 410, 877, 410, 630, 1028, 591, 877, 424, 877, 424, 767, 450, 767, 1163, 424, 1028, 877, 1028, 410, 476], "max_feature": 1159, "max_label": 1163, "recommended_length": 1163}, {"task": "s3/cereals", "feature_lengths": [446, 446, 574, 1425, 1425, 402, 402, 402, 361, 369, 368, 762, 762, 762, 563, 562, 558, 563, 522, 522, 522, 522, 722, 722, 722, 722, 664, 664, 664, 664, 696, 696, 696, 696, 696, 498, 498, 498, 498, 498, 874, 874, 874, 875, 874], "label_lengths": [878, 406, 502, 766, 373, 406, 526, 502, 878, 700, 726, 878, 450, 726, 502, 1429, 406, 567, 700, 766, 526, 878, 578, 878, 766, 373, 502, 1429, 668, 700, 726, 567, 700, 450, 668, 668, 567, 373, 567, 668, 502, 700, 526, 526, 726], "max_feature": 1425, "max_label": 1429, "recommended_length": 1429}, {"task": "s3/friedegg", "feature_lengths": [3732, 3732, 2769, 2769, 6759, 6759, 3893, 3878, 2688, 2688, 2062, 2706, 2706, 2651, 2668, 2669, 3964, 3964, 3964, 3964, 2422, 2422, 2422, 2422, 3044, 3044, 3044, 3044, 2887, 2887, 2887, 2887, 2887, 2353, 2353, 2354, 2353, 2353, 3088, 3088, 3088, 3088, 3088], "label_lengths": [2357, 3092, 3048, 2710, 2692, 3092, 3899, 3736, 2710, 2891, 3736, 6763, 3048, 3048, 2710, 2357, 3092, 3968, 2773, 3968, 3968, 2891, 2426, 2357, 2426, 2673, 2357, 2891, 6763, 2426, 2773, 3899, 2357, 2891, 2891, 2673, 2426, 2673, 3968, 2692, 3048, 3092, 3092], "max_feature": 6759, "max_label": 6763, "recommended_length": 6763}, {"task": "s3/milk", "feature_lengths": [621, 621, 634, 635, 634, 2525, 2525, 2525, 632, 644, 640, 716, 716, 716, 1157, 1159, 1158, 565, 576, 576, 881, 881, 882, 882, 941, 941, 941, 941, 1066, 1066, 1066, 1066, 944, 944, 944, 944, 723, 722, 723, 723, 722, 720, 720, 720, 720, 720], "label_lengths": [720, 885, 647, 1070, 2529, 1163, 720, 720, 625, 1163, 948, 948, 580, 948, 724, 885, 724, 647, 945, 945, 885, 726, 726, 1070, 1070, 638, 625, 1070, 726, 2529, 647, 638, 2529, 580, 885, 638, 1163, 948, 580, 945, 724, 945, 726, 724, 726, 724], "max_feature": 2525, "max_label": 2529, "recommended_length": 2529}, {"task": "s4/sandwich", "feature_lengths": [1374, 1374, 1374, 1374, 1374, 1495, 1495, 1557, 1556, 1556, 1556, 1556, 981, 981, 981, 1424, 1424, 1424, 1424, 1291, 1291, 1291, 1291, 1861, 1861, 1862, 1861, 1847, 1847, 1847, 1847, 1847, 1156, 1155, 1156, 1156, 1156, 929, 929, 929, 929, 929, 1187, 2674, 2674, 786, 786, 786, 786, 1014, 1014, 1014], "label_lengths": [1851, 790, 1560, 1428, 1560, 933, 1018, 933, 933, 2678, 1295, 790, 985, 2678, 1295, 985, 1428, 1499, 1378, 1851, 790, 933, 1160, 2678, 1378, 1865, 933, 1865, 790, 1560, 1295, 1428, 1851, 1428, 1865, 1560, 1295, 1499, 1160, 1378, 1160, 1018, 1160, 1378, 1851, 1160, 1378, 1851, 1560, 1865, 1018, 985], "max_feature": 2674, "max_label": 2678, "recommended_length": 2678}, {"task": "s4/salat", "feature_lengths": [3605, 3605, 3605, 3605, 5148, 5148, 5148, 5148, 4920, 4919, 4920, 4920, 3853, 3853, 3853, 3853, 3143, 3143, 3143, 3143, 4029, 4029, 4029, 4029, 5445, 5445, 5445, 5445, 4230, 4230, 4230, 4230, 4230, 3489, 3489, 3489, 3489, 3432, 3432, 3432, 3432, 3432, 4653, 4653, 4653, 4653, 4653, 1651, 1652, 1651, 1651, 1252, 1251, 1251, 1251], "label_lengths": [5152, 3436, 3857, 3857, 3609, 4033, 5152, 1655, 3609, 4657, 5449, 4924, 3857, 4234, 3147, 4234, 4924, 3857, 3493, 3493, 4924, 1655, 3147, 3493, 3436, 3436, 4924, 1255, 3436, 3609, 1255, 5152, 4657, 5152, 1655, 4033, 1255, 4033, 4234, 3493, 3147, 5449, 4657, 3436, 5449, 1255, 4657, 3609, 4234, 1655, 4657, 3147, 5449, 4033, 4234], "max_feature": 5445, "max_label": 5449, "recommended_length": 5449}, {"task": "s4/pancake", "feature_lengths": [9742, 9742, 9742, 9742, 9737, 7012, 7009, 7012, 7012, 5865, 5865, 5865, 5865, 8196, 8196, 8196, 8196, 5417, 5417, 5417, 5417, 5371, 5371, 5371, 5371, 4036, 4036, 4036, 4036, 6358, 6358, 6358, 6358, 4917, 4917, 4917, 4917, 4917, 5793, 5793, 5793, 5793, 5350, 5350, 5350, 5104, 5350, 3871, 3872, 3871, 3871, 2416, 3341, 3341, 3341, 3341], "label_lengths": [4040, 4921, 5869, 3875, 5797, 5354, 5375, 5421, 5375, 5869, 9746, 4921, 6362, 4921, 6362, 5869, 3875, 3345, 8200, 7016, 3875, 3345, 6362, 3875, 5421, 4921, 5354, 4921, 5354, 5354, 8200, 5421, 7016, 8200, 7016, 9746, 5375, 6362, 5421, 4040, 5797, 5797, 9746, 5869, 9746, 7016, 5797, 3345, 9746, 4040, 3345, 3345, 5354, 4040, 8200, 5375], "max_feature": 9742, "max_label": 9746, "recommended_length": 9746}, {"task": "s4/scrambledegg", "feature_lengths": [2948, 2948, 2949, 2948, 2948, 2053, 2052, 2051, 2052, 2052, 2742, 2742, 2742, 2742, 4429, 4429, 4429, 4429, 3128, 3136, 3100, 3138, 4263, 4263, 4263, 3462, 3462, 3462, 3462, 3450, 3450, 3450, 3450, 3450, 1596, 1596, 1596, 1596, 1596, 3404, 3404, 3404, 3405, 5392, 5392, 5392, 5392, 5392, 2326, 2327, 2326, 2326, 2326, 3691, 3691, 3691, 3478, 3691], "label_lengths": [4433, 3142, 2746, 3695, 4433, 3695, 2330, 2330, 3408, 5396, 2056, 4267, 3454, 2746, 3466, 3142, 1600, 2056, 2056, 3695, 4433, 3466, 3695, 5396, 4267, 2746, 3454, 1600, 3142, 1600, 3408, 3466, 3466, 3454, 4433, 5396, 2330, 2746, 2330, 2952, 2952, 5396, 2952, 2952, 3408, 1600, 3142, 2330, 2952, 3695, 3454, 1600, 2056, 2056, 3408, 4267, 3454, 5396], "max_feature": 5392, "max_label": 5396, "recommended_length": 5396}, {"task": "s4/tea", "feature_lengths": [992, 992, 992, 992, 992, 701, 701, 701, 701, 1102, 1102, 1102, 1102, 1102, 441, 441, 441, 441, 1294, 1294, 1294, 1294, 1294, 487, 487, 487, 488, 487, 568, 568, 568, 568, 360, 360, 360, 360, 360, 1033, 1022, 1033, 1030, 1026, 731, 731, 731, 731, 732, 938, 938, 938, 938, 938, 720, 720, 720, 720, 970, 970, 971, 970], "label_lengths": [364, 705, 572, 572, 735, 996, 1298, 1298, 724, 705, 445, 445, 974, 735, 1037, 724, 942, 491, 942, 1037, 1106, 735, 1298, 974, 491, 1037, 364, 942, 735, 724, 1106, 364, 445, 996, 1037, 1106, 1106, 705, 942, 705, 1106, 724, 1298, 491, 996, 974, 942, 974, 572, 491, 445, 491, 735, 364, 996, 572, 364, 1037, 996, 1298], "max_feature": 1294, "max_label": 1298, "recommended_length": 1298}, {"task": "s4/juice", "feature_lengths": [1519, 1519, 1519, 1519, 1519, 1632, 1632, 1634, 1632, 1517, 1517, 1517, 1517, 1684, 1684, 1684, 1684, 1684, 1611, 1611, 1611, 1611, 1258, 1258, 1258, 1258, 1250, 1250, 1250, 1250, 1388, 1389, 1388, 1388, 1388, 1895, 1895, 1896, 1895, 1895, 1491, 1491, 1491, 1491, 1491, 2948, 2948, 2948, 2948, 1131, 1131, 1131, 1131, 908, 907, 906, 907, 907], "label_lengths": [1636, 1899, 1521, 1262, 1636, 1262, 1688, 1495, 911, 1262, 1615, 1899, 1688, 1392, 2952, 1523, 1615, 1615, 911, 1392, 911, 1623, 2952, 2952, 1899, 1495, 911, 1135, 1254, 911, 1392, 1523, 1495, 1262, 1495, 2952, 1688, 1495, 1135, 1392, 1135, 1523, 1521, 1899, 1392, 1523, 1615, 1899, 1688, 1523, 1254, 1521, 1636, 1254, 1688, 1521, 1254, 1135], "max_feature": 2948, "max_label": 2952, "recommended_length": 2952}, {"task": "s4/coffee", "feature_lengths": [929, 929, 930, 929, 929, 813, 813, 813, 218, 218, 218, 218, 218, 587, 587, 587, 587, 587, 307, 307, 307, 307, 307, 644, 643, 643, 643, 275, 275, 275, 275, 299, 298, 298, 298, 298, 691, 691, 691, 691, 691, 1124, 1124, 131, 1124, 670, 670, 670, 670, 849, 848, 848, 848, 716, 716, 716, 716], "label_lengths": [591, 279, 302, 311, 222, 647, 695, 720, 852, 222, 674, 302, 817, 933, 1128, 933, 1128, 933, 311, 647, 279, 817, 695, 1128, 695, 279, 817, 311, 933, 222, 720, 720, 852, 647, 279, 647, 852, 1128, 222, 695, 720, 591, 852, 222, 933, 674, 674, 695, 302, 591, 311, 674, 302, 311, 591, 302, 591], "max_feature": 1124, "max_label": 1128, "recommended_length": 1128}, {"task": "s4/cereals", "feature_lengths": [454, 454, 455, 454, 454, 770, 770, 770, 770, 762, 762, 762, 762, 763, 916, 916, 916, 916, 916, 897, 897, 897, 897, 674, 674, 674, 674, 674, 738, 738, 738, 738, 735, 717, 716, 716, 716, 458, 458, 458, 459, 458, 708, 708, 708, 708, 708, 802, 802, 803, 802, 847, 847, 847, 847, 847, 744, 744, 744, 744, 744], "label_lengths": [748, 806, 901, 748, 742, 806, 806, 806, 851, 920, 720, 742, 458, 712, 720, 678, 851, 901, 458, 462, 748, 920, 774, 678, 851, 742, 678, 720, 766, 766, 678, 766, 766, 458, 774, 920, 920, 774, 766, 851, 720, 742, 742, 458, 901, 458, 462, 851, 920, 678, 774, 462, 462, 712, 712, 712, 748, 901, 462, 748, 712], "max_feature": 916, "max_label": 920, "recommended_length": 920}, {"task": "s4/friedegg", "feature_lengths": [3014, 3014, 3014, 3014, 2583, 2583, 2583, 2583, 2583, 2181, 2181, 2181, 2181, 2777, 2778, 2777, 2777, 2777, 3102, 3102, 3102, 3102, 3103, 2383, 2384, 2384, 2383, 3091, 3091, 3091, 3091, 5175, 5175, 5175, 5175, 5175, 1598, 1598, 1598, 1598, 2912, 2912, 2912, 2912, 3261, 3261, 3261, 3261, 3261, 1924, 1924, 1924, 1925, 1924, 1556, 1556, 1556, 1556, 1556], "label_lengths": [2387, 2916, 2587, 3106, 1928, 3106, 2587, 3265, 1928, 1928, 3265, 2916, 2185, 3095, 3265, 2781, 3106, 2387, 3106, 2781, 2781, 2781, 2185, 3265, 1560, 2185, 1602, 5179, 3095, 3018, 5179, 1602, 2387, 3095, 2185, 2916, 5179, 3095, 3106, 1602, 3018, 3018, 2587, 1560, 3265, 5179, 1560, 2781, 2387, 2587, 2916, 1560, 5179, 1928, 2587, 1928, 3018, 1560, 1602], "max_feature": 5175, "max_label": 5179, "recommended_length": 5179}, {"task": "s4/milk", "feature_lengths": [1242, 1242, 1242, 1242, 1242, 497, 497, 496, 497, 497, 976, 976, 976, 976, 976, 589, 589, 589, 589, 589, 1214, 1214, 1214, 1214, 1214, 989, 989, 989, 989, 794, 795, 794, 794, 794, 712, 713, 712, 792, 792, 792, 792, 792, 1310, 1310, 1310, 1310, 1310, 1025, 1025, 1025, 1025, 1025, 984, 985, 978, 983, 744, 744, 744, 744], "label_lengths": [1314, 798, 1029, 1218, 501, 993, 1246, 1029, 1314, 501, 798, 1218, 748, 748, 748, 989, 1218, 748, 1314, 989, 1314, 1029, 1218, 501, 993, 1218, 993, 593, 989, 716, 796, 989, 501, 593, 796, 593, 1246, 980, 593, 980, 593, 798, 796, 798, 1246, 1246, 1029, 796, 980, 993, 501, 1029, 1246, 980, 716, 798, 980, 716, 1314, 796], "max_feature": 1310, "max_label": 1314, "recommended_length": 1314}]}, "recommendations": ["重新运行动作原型训练以验证所有48个类别都能被正确加载", "检查修复后的数据质量，确保填充的SIL标签不会影响模型性能", "考虑在数据加载器中添加长度验证机制，防止未来出现类似问题"]}