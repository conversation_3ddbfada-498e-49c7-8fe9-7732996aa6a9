#!/usr/bin/env python3
"""
数据加载器调试脚本 - debug_data_loader.py
调试数据加载器的具体行为，找出为什么只能加载34个类别
"""

import sys
import yaml
import numpy as np
from pathlib import Path
from collections import Counter

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.data_loader import BreakfastDataLoader


def debug_single_task(data_loader, split, task):
    """调试单个任务的加载过程"""
    print(f"\n🔍 调试任务: {split}/{task}")
    
    try:
        # 检查特征文件路径
        task_dir = data_loader.npy_data_path / split / task
        print(f"  特征文件目录: {task_dir}")
        print(f"  目录存在: {task_dir.exists()}")
        
        if task_dir.exists():
            # 列出特征文件
            view_files = sorted([f for f in task_dir.glob("*.npy") if task in f.name])
            print(f"  找到特征文件: {len(view_files)} 个")
            for i, f in enumerate(view_files[:3]):  # 只显示前3个
                print(f"    {i+1}. {f.name}")
            if len(view_files) > 3:
                print(f"    ... 还有 {len(view_files) - 3} 个文件")
        
        # 检查标签文件路径
        label_path = data_loader.npy_label_path / f"{split}_label" / task
        print(f"  标签文件目录: {label_path}")
        print(f"  目录存在: {label_path.exists()}")
        
        if label_path.exists():
            # 列出标签文件
            label_files = list(label_path.glob(f"*{task}*.npy"))
            print(f"  找到标签文件: {len(label_files)} 个")
            for i, f in enumerate(label_files[:3]):  # 只显示前3个
                print(f"    {i+1}. {f.name}")
            if len(label_files) > 3:
                print(f"    ... 还有 {len(label_files) - 3} 个文件")
            
            # 分析第一个标签文件的内容
            if label_files:
                labels = np.load(label_files[0])
                unique_labels = np.unique(labels)
                print(f"  标签文件 {label_files[0].name}:")
                print(f"    总帧数: {len(labels)}")
                print(f"    动作类别数: {len(unique_labels)}")
                print(f"    动作ID: {sorted(unique_labels)}")
        
        # 尝试加载数据
        features, labels = data_loader.load_npy_data(split, task)
        unique_labels = np.unique(labels)
        print(f"  ✅ 成功加载:")
        print(f"    特征形状: {features.shape}")
        print(f"    标签形状: {labels.shape}")
        print(f"    动作类别数: {len(unique_labels)}")
        print(f"    动作ID: {sorted(unique_labels)}")
        
        return unique_labels
        
    except Exception as e:
        print(f"  ❌ 加载失败: {e}")
        return np.array([])


def main():
    """主函数"""
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建数据加载器
    data_loader = BreakfastDataLoader(config)
    
    print("=" * 80)
    print("数据加载器调试")
    print("=" * 80)
    
    print(f"特征数据路径: {data_loader.npy_data_path}")
    print(f"标签数据路径: {data_loader.npy_label_path}")
    print(f"训练分割: {data_loader.train_splits}")
    print(f"测试分割: {data_loader.test_splits}")
    
    # 收集所有动作类别
    all_train_actions = set()
    all_test_actions = set()
    
    # 调试训练数据
    print(f"\n📊 调试训练数据:")
    for split in data_loader.train_splits:
        split_path = data_loader.npy_data_path / split
        if not split_path.exists():
            print(f"  警告: 分割目录不存在: {split_path}")
            continue
        
        print(f"\n  分割: {split}")
        split_actions = set()
        
        # 遍历任务目录
        task_dirs = [d for d in split_path.iterdir() if d.is_dir()]
        print(f"    找到任务目录: {len(task_dirs)} 个")
        
        for task_dir in task_dirs[:3]:  # 只调试前3个任务
            task_name = task_dir.name
            actions = debug_single_task(data_loader, split, task_name)
            split_actions.update(actions)
            all_train_actions.update(actions)
        
        if len(task_dirs) > 3:
            print(f"    ... 还有 {len(task_dirs) - 3} 个任务未详细调试")
            # 快速处理剩余任务
            for task_dir in task_dirs[3:]:
                task_name = task_dir.name
                try:
                    _, labels = data_loader.load_npy_data(split, task_name)
                    unique_labels = np.unique(labels)
                    split_actions.update(unique_labels)
                    all_train_actions.update(unique_labels)
                except Exception as e:
                    print(f"    警告: 无法加载 {split}/{task_name}: {e}")
        
        print(f"    {split} 总动作类别: {len(split_actions)}")
    
    # 调试测试数据
    print(f"\n📊 调试测试数据:")
    for split in data_loader.test_splits:
        split_path = data_loader.npy_data_path / split
        if not split_path.exists():
            print(f"  警告: 分割目录不存在: {split_path}")
            continue
        
        print(f"\n  分割: {split}")
        split_actions = set()
        
        # 遍历任务目录
        task_dirs = [d for d in split_path.iterdir() if d.is_dir()]
        print(f"    找到任务目录: {len(task_dirs)} 个")
        
        for task_dir in task_dirs[:2]:  # 只调试前2个任务
            task_name = task_dir.name
            actions = debug_single_task(data_loader, split, task_name)
            split_actions.update(actions)
            all_test_actions.update(actions)
        
        if len(task_dirs) > 2:
            print(f"    ... 还有 {len(task_dirs) - 2} 个任务未详细调试")
            # 快速处理剩余任务
            for task_dir in task_dirs[2:]:
                task_name = task_dir.name
                try:
                    _, labels = data_loader.load_npy_data(split, task_name)
                    unique_labels = np.unique(labels)
                    split_actions.update(unique_labels)
                    all_test_actions.update(unique_labels)
                except Exception as e:
                    print(f"    警告: 无法加载 {split}/{task_name}: {e}")
        
        print(f"    {split} 总动作类别: {len(split_actions)}")
    
    # 总结
    print(f"\n" + "=" * 80)
    print("调试总结")
    print("=" * 80)
    
    all_actions = all_train_actions.union(all_test_actions)
    
    print(f"训练集动作类别数: {len(all_train_actions)}")
    print(f"测试集动作类别数: {len(all_test_actions)}")
    print(f"总动作类别数: {len(all_actions)}")
    
    print(f"\n训练集动作ID: {sorted(all_train_actions)}")
    print(f"测试集动作ID: {sorted(all_test_actions)}")
    
    # 检查缺失的动作类别
    expected_actions = set(range(48))  # 假设有48个动作类别
    missing_actions = expected_actions - all_actions
    
    if missing_actions:
        print(f"\n❌ 缺失的动作类别: {sorted(missing_actions)}")
    else:
        print(f"\n✅ 所有动作类别都已找到")


if __name__ == "__main__":
    main()
