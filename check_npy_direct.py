#!/usr/bin/env python3
"""直接检查NPY标签文件中的动作类别"""

import numpy as np
from pathlib import Path
import json

def check_npy_labels_direct():
    """直接检查NPY标签文件"""
    
    npy_label_path = Path("/data2/syd_data/Breakfast_Data/segmentation_coarse_npy")
    label_map_path = Path("/data2/syd_data/Breakfast_Data/label_map.json")
    
    # 加载标签映射
    with open(label_map_path, 'r') as f:
        label_map = json.load(f)
    
    # 统计所有动作
    all_actions = set()
    action_files = {}  # 记录每个动作出现在哪些文件中
    
    # 遍历训练集
    for split in ["s1", "s2", "s3"]:
        split_dir = npy_label_path / f"{split}_label"
        if not split_dir.exists():
            print(f"警告: {split_dir} 不存在")
            continue
            
        # 遍历任务目录
        for task_dir in split_dir.iterdir():
            if not task_dir.is_dir():
                continue
                
            # 遍历标签文件
            for label_file in task_dir.glob("*.npy"):
                try:
                    labels = np.load(label_file)
                    unique_actions = np.unique(labels)
                    
                    for action_id in unique_actions:
                        all_actions.add(int(action_id))
                        
                        # 记录文件
                        if action_id not in action_files:
                            action_files[action_id] = []
                        action_files[action_id].append(str(label_file))
                        
                except Exception as e:
                    print(f"错误: 无法加载 {label_file}: {e}")
    
    print(f"在NPY文件中找到 {len(all_actions)} 个唯一动作")
    
    # 检查缺失的动作
    missing_actions = [14, 19, 29, 32, 34, 37, 40, 42]  # 之前发现缺失的动作ID
    
    print("\n=== 检查缺失动作 ===")
    for action_id in missing_actions:
        action_name = label_map.get(str(action_id), "UNKNOWN")
        if action_id in all_actions:
            print(f"✓ {action_id} ({action_name}): 找到，出现在 {len(action_files[action_id])} 个文件中")
            # 显示前3个文件
            for file_path in action_files[action_id][:3]:
                print(f"  - {file_path}")
        else:
            print(f"✗ {action_id} ({action_name}): 未找到")
    
    # 显示所有找到的动作
    print(f"\n所有找到的动作ID: {sorted(all_actions)}")

if __name__ == "__main__":
    check_npy_labels_direct() 