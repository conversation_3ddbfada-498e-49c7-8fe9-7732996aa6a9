#!/usr/bin/env python3
"""简化版动作原型训练脚本"""

import numpy as np
from pathlib import Path
import json
from collections import defaultdict
from datetime import datetime
import pickle

def train_action_prototypes():
    """训练动作原型特征"""
    
    # 路径配置
    npy_data_path = Path("/data2/syd_data/Breakfast_Data/breakfast_data_npy")
    npy_label_path = Path("/data2/syd_data/Breakfast_Data/segmentation_coarse_npy")
    label_map_path = Path("/data2/syd_data/Breakfast_Data/label_map.json")
    output_base = Path("/data2/syd_data/Breakfast_Data/Outputs/Action_Prototype")
    
    # 加载标签映射
    with open(label_map_path, 'r') as f:
        label_map = json.load(f)
    num_classes = len(label_map)
    
    print("="*60)
    print("开始训练动作原型特征")
    print(f"动作类别数: {num_classes}")
    print("="*60)
    
    # 收集每个动作的特征
    action_features = defaultdict(list)
    total_samples = 0
    
    # 遍历训练集
    for split in ["s1", "s2", "s3"]:
        split_path = npy_data_path / split
        if not split_path.exists():
            print(f"警告: {split_path} 不存在")
            continue
            
        # 遍历任务目录
        for task_dir in split_path.iterdir():
            if not task_dir.is_dir():
                continue
                
            task_name = task_dir.name
            
            # 获取所有特征文件
            feature_files = sorted(task_dir.glob("*.npy"))
            print(f"\n处理 {split}/{task_name}: {len(feature_files)} 个文件")
            
            # 处理每个特征文件
            for feature_file in feature_files:
                try:
                    # 加载特征
                    features_data = np.load(feature_file)
                    
                    # 找到对应的标签文件
                    label_dir = npy_label_path / f"{split}_label" / task_name
                    label_file = label_dir / f"{feature_file.stem}.npy"
                    
                    if not label_file.exists():
                        continue
                        
                    # 加载标签
                    labels = np.load(label_file)
                    
                    # 确保长度匹配
                    min_len = min(len(features_data), len(labels))
                    features_data = features_data[:min_len]
                    labels = labels[:min_len]
                    
                    # 提取特征（去掉帧索引）
                    if features_data.shape[1] == 65:
                        features = features_data[:, 1:]
                    else:
                        features = features_data
                    
                    # 按动作分组
                    for i, label in enumerate(labels):
                        action_id = int(label)
                        action_features[action_id].append(features[i])
                        total_samples += 1
                        
                except Exception as e:
                    print(f"  错误: {feature_file.name}: {e}")
    
    print(f"\n收集完成: {len(action_features)} 个动作类别, 总计 {total_samples} 个样本")
    
    # 计算原型特征
    print("\n计算动作原型...")
    prototypes = np.zeros((num_classes, 64), dtype=np.float32)
    samples_per_action = {}
    prototype_variances = {}
    
    for action_id, features_list in action_features.items():
        if features_list:
            # 转换为numpy数组
            features_array = np.array(features_list)
            
            # 计算均值作为原型
            prototype = np.mean(features_array, axis=0)
            prototypes[action_id] = prototype
            
            # 统计信息
            samples_per_action[action_id] = len(features_list)
            prototype_variances[action_id] = np.mean(np.var(features_array, axis=0))
            
            action_name = label_map.get(str(action_id), f"Unknown_{action_id}")
            print(f"  动作 {action_id} ({action_name}): {len(features_list)} 个样本, 方差: {prototype_variances[action_id]:.6f}")
    
    # 保存结果
    output_base.mkdir(parents=True, exist_ok=True)
    
    # 保存原型
    model_params_dir = output_base / "Model_parameters"
    model_params_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    
    # 保存为numpy格式
    np.save(model_params_dir / f"action_prototypes_{timestamp}.npy", prototypes)
    
    # 保存为pickle格式（包含更多信息）
    prototype_data = {
        'prototypes': prototypes,
        'num_classes': num_classes,
        'samples_per_action': samples_per_action,
        'prototype_variances': prototype_variances,
        'timestamp': timestamp
    }
    with open(model_params_dir / f"action_prototypes_{timestamp}.pkl", 'wb') as f:
        pickle.dump(prototype_data, f)
    
    # 保存统计信息
    raw_data_dir = output_base / "Raw_data"
    raw_data_dir.mkdir(exist_ok=True)
    
    stats = {
        'total_samples': total_samples,
        'num_classes': num_classes,
        'num_trained_classes': len(action_features),
        'samples_per_action': samples_per_action,
        'prototype_variances': prototype_variances,
        'timestamp': timestamp
    }
    
    with open(raw_data_dir / f"training_stats_{timestamp}.json", 'w') as f:
        json.dump(stats, f, indent=2)
    
    # 生成报告
    print("\n="*60)
    print("训练完成!")
    print(f"训练了 {len(action_features)} / {num_classes} 个动作类别")
    print(f"总计 {total_samples} 个训练样本")
    print(f"结果保存到: {output_base}")
    
    # 检查未训练的动作
    untrained_actions = []
    for i in range(num_classes):
        if i not in action_features:
            action_name = label_map.get(str(i), f"Unknown_{i}")
            untrained_actions.append(f"  {i}: {action_name}")
    
    if untrained_actions:
        print(f"\n警告: {len(untrained_actions)} 个动作类别没有训练数据:")
        for action in untrained_actions[:10]:  # 只显示前10个
            print(action)
        if len(untrained_actions) > 10:
            print(f"  ... 还有 {len(untrained_actions) - 10} 个")
    
    print("="*60)

if __name__ == "__main__":
    train_action_prototypes() 