#!/usr/bin/env python3
"""
数据长度不匹配修复脚本 - fix_data_length_mismatch.py
修复特征文件和标签文件长度不匹配的问题

修复策略：
1. 分析所有文件的长度分布
2. 以最长序列为基准
3. 短序列用SIL(ID=0)填充特征，用零向量填充特征
4. 生成修复报告
"""

import sys
import json
import yaml
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict, Counter
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.data_loader import BreakfastDataLoader


class DataLengthFixer:
    """数据长度不匹配修复器"""

    def __init__(self, config: Dict):
        self.config = config
        self.data_loader = BreakfastDataLoader(config)
        
        # 路径配置
        self.npy_data_path = Path(config['data']['npy_data_path'])
        self.npy_label_path = Path(config['data']['npy_label_path'])
        
        # 修复统计
        self.fix_stats = {
            'total_files': 0,
            'mismatched_files': 0,
            'fixed_files': 0,
            'length_distribution': Counter(),
            'mismatch_details': [],
            'backup_created': False
        }

    def analyze_length_distribution(self) -> Dict:
        """分析所有文件的长度分布"""
        print("=" * 60)
        print("分析数据长度分布")
        print("=" * 60)
        
        length_analysis = {
            'feature_lengths': defaultdict(list),
            'label_lengths': defaultdict(list),
            'mismatches': [],
            'max_length_per_task': {}
        }
        
        all_splits = ['s1', 's2', 's3', 's4']
        
        for split in all_splits:
            print(f"\n分析分割: {split}")
            
            split_path = self.npy_data_path / split
            if not split_path.exists():
                continue
            
            # 遍历任务目录
            for task_dir in split_path.iterdir():
                if not task_dir.is_dir():
                    continue
                
                task_name = task_dir.name
                task_key = f"{split}/{task_name}"
                
                # 获取特征文件
                feature_files = sorted([f for f in task_dir.glob("*.npy") if task_name in f.name])
                
                # 获取标签文件
                label_path = self.npy_label_path / f"{split}_label" / task_name
                label_files = list(label_path.glob(f"*{task_name}*.npy")) if label_path.exists() else []
                
                if not feature_files or not label_files:
                    continue
                
                # 分析每个特征文件
                feature_lengths = []
                for feature_file in feature_files:
                    try:
                        features = np.load(feature_file)
                        feature_length = len(features)
                        feature_lengths.append(feature_length)
                        length_analysis['feature_lengths'][task_key].append(feature_length)
                    except Exception as e:
                        print(f"  警告: 无法加载特征文件 {feature_file}: {e}")
                
                # 分析标签文件（通常每个任务只有一个或几个标签文件）
                label_lengths = []
                for label_file in label_files:
                    try:
                        labels = np.load(label_file)
                        label_length = len(labels)
                        label_lengths.append(label_length)
                        length_analysis['label_lengths'][task_key].append(label_length)
                    except Exception as e:
                        print(f"  警告: 无法加载标签文件 {label_file}: {e}")
                
                # 检查长度不匹配
                if feature_lengths and label_lengths:
                    max_feature_len = max(feature_lengths)
                    max_label_len = max(label_lengths)
                    min_feature_len = min(feature_lengths)
                    
                    # 记录最大长度
                    length_analysis['max_length_per_task'][task_key] = max(max_feature_len, max_label_len)
                    
                    # 检查是否有不匹配
                    if max_feature_len != max_label_len or min_feature_len != max_label_len:
                        mismatch_info = {
                            'task': task_key,
                            'feature_lengths': feature_lengths,
                            'label_lengths': label_lengths,
                            'max_feature': max_feature_len,
                            'max_label': max_label_len,
                            'recommended_length': max(max_feature_len, max_label_len)
                        }
                        length_analysis['mismatches'].append(mismatch_info)
                        self.fix_stats['mismatched_files'] += len(feature_files)
                        
                        print(f"  ❌ {task_name}: 特征长度{feature_lengths}, 标签长度{label_lengths}")
                    else:
                        print(f"  ✅ {task_name}: 长度一致 ({max_feature_len})")
                
                self.fix_stats['total_files'] += len(feature_files)
        
        print(f"\n📊 长度分析总结:")
        print(f"  总文件数: {self.fix_stats['total_files']}")
        print(f"  不匹配文件数: {self.fix_stats['mismatched_files']}")
        print(f"  不匹配任务数: {len(length_analysis['mismatches'])}")
        
        return length_analysis

    def create_backup(self):
        """创建数据备份"""
        print("\n" + "=" * 60)
        print("创建数据备份")
        print("=" * 60)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_base = Path(f"/data2/syd_data/Breakfast_Data/backup_{timestamp}")
        
        try:
            # 备份特征数据
            feature_backup = backup_base / "breakfast_data_npy"
            print(f"备份特征数据到: {feature_backup}")
            shutil.copytree(self.npy_data_path, feature_backup)
            
            # 备份标签数据
            label_backup = backup_base / "segmentation_coarse_npy"
            print(f"备份标签数据到: {label_backup}")
            shutil.copytree(self.npy_label_path, label_backup)
            
            self.fix_stats['backup_created'] = True
            self.fix_stats['backup_path'] = str(backup_base)
            print(f"✅ 备份创建成功: {backup_base}")
            
        except Exception as e:
            print(f"❌ 备份创建失败: {e}")
            raise

    def fix_length_mismatches(self, length_analysis: Dict, create_backup: bool = True):
        """修复长度不匹配问题"""
        print("\n" + "=" * 60)
        print("修复长度不匹配问题")
        print("=" * 60)
        
        if create_backup:
            self.create_backup()
        
        mismatches = length_analysis['mismatches']
        if not mismatches:
            print("✅ 没有发现长度不匹配问题")
            return
        
        print(f"开始修复 {len(mismatches)} 个不匹配的任务...")
        
        for mismatch in mismatches:
            task_key = mismatch['task']
            target_length = mismatch['recommended_length']
            
            print(f"\n修复任务: {task_key} (目标长度: {target_length})")
            
            # 解析split和task
            split, task_name = task_key.split('/')
            
            # 修复特征文件
            self._fix_feature_files(split, task_name, target_length)
            
            # 修复标签文件
            self._fix_label_files(split, task_name, target_length)
            
            self.fix_stats['fixed_files'] += 1

    def _fix_feature_files(self, split: str, task_name: str, target_length: int):
        """修复特征文件长度"""
        task_dir = self.npy_data_path / split / task_name
        feature_files = sorted([f for f in task_dir.glob("*.npy") if task_name in f.name])
        
        for feature_file in feature_files:
            try:
                # 加载原始特征
                features = np.load(feature_file)
                current_length = len(features)
                
                if current_length < target_length:
                    # 需要填充
                    padding_length = target_length - current_length
                    
                    # 创建填充数据：帧索引递增，特征为零向量
                    if features.shape[1] == 65:  # [frame_idx, 64 features]
                        # 获取最后一帧的索引
                        last_frame_idx = features[-1, 0] if len(features) > 0 else 0
                        
                        # 创建填充帧
                        padding_frames = np.zeros((padding_length, 65), dtype=features.dtype)
                        for i in range(padding_length):
                            padding_frames[i, 0] = last_frame_idx + i + 1  # 递增帧索引
                            # 特征部分保持为零
                        
                        # 拼接
                        extended_features = np.vstack([features, padding_frames])
                    else:
                        # 如果是64维特征（已去除帧索引）
                        padding_features = np.zeros((padding_length, features.shape[1]), dtype=features.dtype)
                        extended_features = np.vstack([features, padding_features])
                    
                    # 保存修复后的文件
                    np.save(feature_file, extended_features)
                    print(f"    特征文件 {feature_file.name}: {current_length} -> {target_length}")
                
                elif current_length > target_length:
                    # 需要截断（通常不应该发生，但为了安全起见）
                    truncated_features = features[:target_length]
                    np.save(feature_file, truncated_features)
                    print(f"    特征文件 {feature_file.name}: {current_length} -> {target_length} (截断)")
                
            except Exception as e:
                print(f"    ❌ 修复特征文件 {feature_file.name} 失败: {e}")

    def _fix_label_files(self, split: str, task_name: str, target_length: int):
        """修复标签文件长度"""
        label_path = self.npy_label_path / f"{split}_label" / task_name
        if not label_path.exists():
            return
        
        label_files = list(label_path.glob(f"*{task_name}*.npy"))
        
        for label_file in label_files:
            try:
                # 加载原始标签
                labels = np.load(label_file)
                current_length = len(labels)
                
                if current_length < target_length:
                    # 需要填充：用SIL(ID=0)填充
                    padding_length = target_length - current_length
                    padding_labels = np.zeros(padding_length, dtype=labels.dtype)  # SIL的ID是0
                    
                    extended_labels = np.concatenate([labels, padding_labels])
                    
                    # 保存修复后的文件
                    np.save(label_file, extended_labels)
                    print(f"    标签文件 {label_file.name}: {current_length} -> {target_length}")
                
                elif current_length > target_length:
                    # 需要截断
                    truncated_labels = labels[:target_length]
                    np.save(label_file, truncated_labels)
                    print(f"    标签文件 {label_file.name}: {current_length} -> {target_length} (截断)")
                
            except Exception as e:
                print(f"    ❌ 修复标签文件 {label_file.name} 失败: {e}")

    def verify_fix(self) -> bool:
        """验证修复结果"""
        print("\n" + "=" * 60)
        print("验证修复结果")
        print("=" * 60)
        
        # 重新分析长度分布
        new_analysis = self.analyze_length_distribution()
        
        if not new_analysis['mismatches']:
            print("✅ 修复验证通过：所有文件长度现在都匹配了！")
            return True
        else:
            print(f"❌ 修复验证失败：仍有 {len(new_analysis['mismatches'])} 个不匹配")
            return False

    def generate_report(self, length_analysis: Dict):
        """生成修复报告"""
        print("\n" + "=" * 60)
        print("生成修复报告")
        print("=" * 60)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'fix_stats': self.fix_stats,
            'length_analysis': {
                'total_tasks': len(length_analysis['max_length_per_task']),
                'mismatched_tasks': len(length_analysis['mismatches']),
                'mismatch_details': length_analysis['mismatches']
            },
            'recommendations': [
                "重新运行动作原型训练以验证所有48个类别都能被正确加载",
                "检查修复后的数据质量，确保填充的SIL标签不会影响模型性能",
                "考虑在数据加载器中添加长度验证机制，防止未来出现类似问题"
            ]
        }
        
        # 保存报告
        report_path = "data_length_fix_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"修复报告已保存到: {report_path}")
        
        # 打印摘要
        print(f"\n📋 修复摘要:")
        print(f"  总文件数: {self.fix_stats['total_files']}")
        print(f"  不匹配文件数: {self.fix_stats['mismatched_files']}")
        print(f"  已修复文件数: {self.fix_stats['fixed_files']}")
        print(f"  备份创建: {'✅' if self.fix_stats['backup_created'] else '❌'}")
        if self.fix_stats.get('backup_path'):
            print(f"  备份路径: {self.fix_stats['backup_path']}")


def main():
    """主函数"""
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建修复器
    fixer = DataLengthFixer(config)
    
    print("🔧 数据长度不匹配修复工具")
    print("=" * 80)
    
    # 1. 分析长度分布
    length_analysis = fixer.analyze_length_distribution()
    
    # 2. 询问用户是否继续修复
    if length_analysis['mismatches']:
        print(f"\n发现 {len(length_analysis['mismatches'])} 个任务存在长度不匹配问题")
        response = input("是否继续修复？(y/N): ").strip().lower()
        
        if response == 'y':
            # 3. 修复长度不匹配
            fixer.fix_length_mismatches(length_analysis, create_backup=True)
            
            # 4. 验证修复结果
            success = fixer.verify_fix()
            
            # 5. 生成报告
            fixer.generate_report(length_analysis)
            
            if success:
                print("\n🎉 修复完成！现在可以重新运行动作原型训练来验证所有48个类别都能被正确加载。")
            else:
                print("\n⚠️ 修复可能不完整，请检查报告并手动处理剩余问题。")
        else:
            print("修复已取消")
    else:
        print("\n✅ 没有发现长度不匹配问题，数据状态良好！")


if __name__ == "__main__":
    main()
