#!/usr/bin/env python3
"""
动作类别分析脚本 - check_type.py
分析训练集中动作类别的分布情况，找出缺失的动作类别

主要功能：
1. 加载label_map.json获取所有动作类别定义
2. 分析训练集中实际出现的动作类别
3. 找出缺失的动作类别及其原因
4. 生成详细的统计报告
"""

import sys
import json
import numpy as np
import yaml
from pathlib import Path
from typing import Dict, Set, List, Tuple
from collections import defaultdict, Counter

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.data_loader import BreakfastDataLoader


class ActionTypeAnalyzer:
    """动作类别分析器"""

    def __init__(self, config: Dict):
        self.config = config
        self.data_loader = BreakfastDataLoader(config)
        
        # 加载标签映射
        self.label_map_path = Path(config['data']['label_map_path'])
        self.label_map = self._load_label_map()
        
        # 分析结果
        self.analysis_results = {
            'total_defined_actions': 0,
            'total_found_actions': 0,
            'missing_actions': [],
            'found_actions': {},
            'action_distribution': {},
            'split_distribution': {}
        }

    def _load_label_map(self) -> Dict[int, str]:
        """加载标签映射文件"""
        if not self.label_map_path.exists():
            print(f"错误: 标签映射文件不存在: {self.label_map_path}")
            return {}
        
        with open(self.label_map_path, 'r', encoding='utf-8') as f:
            label_data = json.load(f)
        
        # 转换为 {id: name} 格式
        if isinstance(label_data, dict):
            # 检查是否是 {id: name} 格式
            if all(isinstance(k, (int, str)) and isinstance(v, str) for k, v in label_data.items()):
                return {int(k): v for k, v in label_data.items()}
            # 检查是否是 {name: id} 格式
            elif all(isinstance(k, str) and isinstance(v, (int, str)) for k, v in label_data.items()):
                return {int(v): k for k, v in label_data.items()}
        
        print(f"警告: 无法解析标签映射文件格式")
        return {}

    def analyze_training_data(self) -> Dict:
        """分析训练数据中的动作类别分布"""
        print("=" * 60)
        print("开始分析训练数据中的动作类别分布")
        print("=" * 60)

        found_actions = set()
        action_counts = Counter()
        split_counts = defaultdict(lambda: defaultdict(int))

        # 遍历所有训练数据
        for split in self.data_loader.train_splits:
            split_path = self.data_loader.npy_data_path / split
            if not split_path.exists():
                print(f"警告: 分割目录不存在: {split_path}")
                continue

            print(f"\n分析分割: {split}")
            split_actions = set()

            # 遍历任务目录
            for task_dir in split_path.iterdir():
                if not task_dir.is_dir():
                    continue

                task_name = task_dir.name

                try:
                    # 加载标签数据
                    _, labels = self.data_loader.load_npy_data(split, task_name)

                    # 统计动作类别
                    unique_labels = np.unique(labels)
                    for label in unique_labels:
                        action_id = int(label)
                        found_actions.add(action_id)
                        split_actions.add(action_id)
                        action_counts[action_id] += np.sum(labels == label)
                        split_counts[split][action_id] += np.sum(labels == label)

                    print(f"  {task_name}: {len(unique_labels)} 个动作类别, {len(labels)} 帧")

                except Exception as e:
                    print(f"  警告: 无法加载 {split}/{task_name}: {e}")

            print(f"  {split} 总计: {len(split_actions)} 个动作类别")

        # 更新分析结果
        self.analysis_results['total_defined_actions'] = len(self.label_map)
        self.analysis_results['total_found_actions'] = len(found_actions)
        self.analysis_results['found_actions'] = found_actions
        self.analysis_results['action_distribution'] = dict(action_counts)
        self.analysis_results['split_distribution'] = dict(split_counts)

        # 找出缺失的动作类别
        all_defined_actions = set(self.label_map.keys())
        missing_actions = all_defined_actions - found_actions
        self.analysis_results['missing_actions'] = sorted(missing_actions)

        return self.analysis_results

    def analyze_test_data(self) -> Dict:
        """分析测试数据中的动作类别分布"""
        print("\n" + "=" * 60)
        print("开始分析测试数据中的动作类别分布")
        print("=" * 60)

        test_found_actions = set()
        test_action_counts = Counter()

        # 遍历所有测试数据
        for split in self.data_loader.test_splits:
            split_path = self.data_loader.npy_data_path / split
            if not split_path.exists():
                print(f"警告: 分割目录不存在: {split_path}")
                continue

            print(f"\n分析测试分割: {split}")
            split_actions = set()

            # 遍历任务目录
            for task_dir in split_path.iterdir():
                if not task_dir.is_dir():
                    continue

                task_name = task_dir.name

                try:
                    # 加载标签数据
                    _, labels = self.data_loader.load_npy_data(split, task_name)

                    # 统计动作类别
                    unique_labels = np.unique(labels)
                    for label in unique_labels:
                        action_id = int(label)
                        test_found_actions.add(action_id)
                        split_actions.add(action_id)
                        test_action_counts[action_id] += np.sum(labels == label)

                    print(f"  {task_name}: {len(unique_labels)} 个动作类别, {len(labels)} 帧")

                except Exception as e:
                    print(f"  警告: 无法加载 {split}/{task_name}: {e}")

            print(f"  {split} 总计: {len(split_actions)} 个动作类别")

        # 保存测试数据分析结果
        self.analysis_results['test_found_actions'] = test_found_actions
        self.analysis_results['test_action_distribution'] = dict(test_action_counts)

        return test_found_actions

    def perform_verification(self):
        """执行两次验证"""
        print("\n" + "=" * 80)
        print("执行数据完整性验证")
        print("=" * 80)

        # 验证1: 训练集类别数量
        train_actions = self.analysis_results['found_actions']
        print(f"\n🔍 验证1: 训练集(s1,s2,s3)类别数量验证")
        print(f"   实际发现的类别数: {len(train_actions)}")
        print(f"   预期类别数: 34")
        if len(train_actions) == 34:
            print("   ✅ 验证通过: 训练集确实包含34个动作类别")
        else:
            print("   ❌ 验证失败: 训练集类别数量不符合预期")

        # 分析测试集
        test_actions = self.analyze_test_data()

        # 验证2: 训练集+测试集总类别数量
        all_actions = train_actions.union(test_actions)
        print(f"\n🔍 验证2: 训练集+测试集总类别数量验证")
        print(f"   训练集类别数: {len(train_actions)}")
        print(f"   测试集类别数: {len(test_actions)}")
        print(f"   合并后总类别数: {len(all_actions)}")
        print(f"   预期总类别数: 48")

        if len(all_actions) == 48:
            print("   ✅ 验证通过: 训练集+测试集刚好包含48个动作类别")
        else:
            print("   ❌ 验证失败: 总类别数量不符合预期")

        # 分析测试集独有的类别
        test_only_actions = test_actions - train_actions
        train_only_actions = train_actions - test_actions

        print(f"\n📊 详细分析:")
        print(f"   训练集独有类别数: {len(train_only_actions)}")
        print(f"   测试集独有类别数: {len(test_only_actions)}")
        print(f"   共同类别数: {len(train_actions.intersection(test_actions))}")

        if test_only_actions:
            print(f"\n   测试集独有的动作类别:")
            for action_id in sorted(test_only_actions):
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                print(f"     • ID {action_id:2d}: {action_name}")

        if train_only_actions:
            print(f"\n   训练集独有的动作类别:")
            for action_id in sorted(train_only_actions):
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                print(f"     • ID {action_id:2d}: {action_name}")

        # 更新分析结果
        self.analysis_results['verification'] = {
            'train_count_correct': len(train_actions) == 34,
            'total_count_correct': len(all_actions) == 48,
            'test_only_actions': sorted(test_only_actions),
            'train_only_actions': sorted(train_only_actions),
            'common_actions': len(train_actions.intersection(test_actions))
        }

    def generate_report(self) -> str:
        """生成详细的分析报告"""
        report = []
        report.append("=" * 80)
        report.append("动作类别分析报告")
        report.append("=" * 80)
        
        # 基本统计
        report.append(f"\n📊 基本统计:")
        report.append(f"  • 定义的动作类别总数: {self.analysis_results['total_defined_actions']}")
        report.append(f"  • 训练集中出现的动作类别数: {self.analysis_results['total_found_actions']}")
        report.append(f"  • 缺失的动作类别数: {len(self.analysis_results['missing_actions'])}")
        report.append(f"  • 覆盖率: {self.analysis_results['total_found_actions']/self.analysis_results['total_defined_actions']*100:.1f}%")
        
        # 缺失的动作类别
        if self.analysis_results['missing_actions']:
            report.append(f"\n❌ 缺失的动作类别 ({len(self.analysis_results['missing_actions'])} 个):")
            for action_id in self.analysis_results['missing_actions']:
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                report.append(f"  • ID {action_id:2d}: {action_name}")
        
        # 出现的动作类别统计
        report.append(f"\n✅ 训练集中出现的动作类别 ({self.analysis_results['total_found_actions']} 个):")
        
        # 按样本数量排序
        sorted_actions = sorted(
            self.analysis_results['action_distribution'].items(),
            key=lambda x: x[1], reverse=True
        )
        
        for action_id, count in sorted_actions:
            action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
            report.append(f"  • ID {action_id:2d}: {action_name:<20} - {count:5d} 个样本")
        
        # 各分割的动作分布
        report.append(f"\n📈 各分割的动作分布:")
        for split, split_data in self.analysis_results['split_distribution'].items():
            report.append(f"\n  {split.upper()} ({len(split_data)} 个动作类别):")
            sorted_split_actions = sorted(split_data.items(), key=lambda x: x[1], reverse=True)
            for action_id, count in sorted_split_actions[:10]:  # 只显示前10个
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                report.append(f"    • ID {action_id:2d}: {action_name:<15} - {count:4d} 样本")
            if len(sorted_split_actions) > 10:
                report.append(f"    ... 还有 {len(sorted_split_actions) - 10} 个动作类别")
        
        # 可能的原因分析
        report.append(f"\n🔍 缺失动作类别的可能原因:")
        report.append(f"  1. 某些动作在训练集(s1,s2,s3)中确实不存在，只在测试集(s4)中出现")
        report.append(f"  2. 某些动作是罕见动作，在数据集中出现频率极低")
        report.append(f"  3. 某些动作可能是标注错误或数据预处理问题导致的")
        report.append(f"  4. SIL(静默)类别可能被特殊处理或过滤")
        
        # 建议
        report.append(f"\n💡 建议:")
        report.append(f"  • 检查测试集(s4)中是否包含这些缺失的动作类别")
        report.append(f"  • 考虑在原型计算时为缺失类别使用零向量或随机初始化")
        report.append(f"  • 在模型训练时添加类别平衡策略处理数据不平衡问题")
        
        return "\n".join(report)

    def save_detailed_analysis(self, output_path: str = "action_analysis_detailed.json"):
        """保存详细的分析结果到JSON文件"""
        detailed_results = {
            'summary': {
                'total_defined_actions': self.analysis_results['total_defined_actions'],
                'total_found_actions': self.analysis_results['total_found_actions'],
                'missing_count': len(self.analysis_results['missing_actions']),
                'coverage_rate': self.analysis_results['total_found_actions']/self.analysis_results['total_defined_actions']
            },
            'label_map': self.label_map,
            'missing_actions': [
                {
                    'id': int(action_id),
                    'name': self.label_map.get(action_id, f"未知动作_{action_id}")
                }
                for action_id in self.analysis_results['missing_actions']
            ],
            'found_actions': [
                {
                    'id': int(action_id),
                    'name': self.label_map.get(action_id, f"未知动作_{action_id}"),
                    'total_samples': int(self.analysis_results['action_distribution'][action_id]),
                    'split_distribution': {
                        split: int(self.analysis_results['split_distribution'][split].get(action_id, 0))
                        for split in self.data_loader.train_splits
                    }
                }
                for action_id in sorted(self.analysis_results['found_actions'])
            ]
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细分析结果已保存到: {output_path}")


def main():
    """主函数"""
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 创建分析器
    analyzer = ActionTypeAnalyzer(config)

    # 执行分析
    results = analyzer.analyze_training_data()

    # 执行验证
    analyzer.perform_verification()

    # 生成并显示报告
    report = analyzer.generate_report()
    print(report)

    # 保存详细分析结果
    analyzer.save_detailed_analysis()

    print(f"\n🎯 分析完成!")
    print(f"   • 总动作类别: {results['total_defined_actions']}")
    print(f"   • 训练集中出现: {results['total_found_actions']}")
    print(f"   • 缺失类别: {len(results['missing_actions'])}")

    # 显示验证结果摘要
    verification = analyzer.analysis_results.get('verification', {})
    print(f"\n🔍 验证结果摘要:")
    print(f"   • 训练集34类验证: {'✅ 通过' if verification.get('train_count_correct') else '❌ 失败'}")
    print(f"   • 总计48类验证: {'✅ 通过' if verification.get('total_count_correct') else '❌ 失败'}")


if __name__ == "__main__":
    main()
