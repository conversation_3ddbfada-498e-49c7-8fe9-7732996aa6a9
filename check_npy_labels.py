#!/usr/bin/env python3
"""
NPY标签文件分析脚本 - check_npy_labels.py
直接分析转换后的NPY标签文件，确认是否包含所有动作类别
"""

import sys
import json
import yaml
import numpy as np
from pathlib import Path
from typing import Dict, Set
from collections import Counter

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class NpyLabelAnalyzer:
    """NPY标签文件分析器"""

    def __init__(self, config: Dict):
        self.config = config
        
        # 路径配置
        self.npy_label_path = Path(config['data']['npy_label_path'])
        self.label_map_path = Path(config['data']['label_map_path'])
        
        # 加载标签映射
        self.label_map = self._load_label_map()
        
        # 分析结果
        self.npy_results = {
            'train_actions': set(),
            'test_actions': set(),
            'all_actions': set(),
            'action_counts': Counter(),
            'split_distribution': {},
            'file_count': 0,
            'total_frames': 0
        }

    def _load_label_map(self) -> Dict[int, str]:
        """加载标签映射文件"""
        if not self.label_map_path.exists():
            print(f"错误: 标签映射文件不存在: {self.label_map_path}")
            return {}
        
        with open(self.label_map_path, 'r', encoding='utf-8') as f:
            label_data = json.load(f)
        
        # 转换为 {id: name} 格式
        if isinstance(label_data, dict):
            if all(isinstance(k, (int, str)) and isinstance(v, str) for k, v in label_data.items()):
                return {int(k): v for k, v in label_data.items()}
        
        return {}

    def analyze_npy_labels(self):
        """分析NPY标签文件"""
        print("=" * 60)
        print("开始分析NPY标签文件")
        print("=" * 60)
        
        # 定义训练和测试分割
        train_splits = ['s1', 's2', 's3']
        test_splits = ['s4']
        all_splits = train_splits + test_splits
        
        for split in all_splits:
            print(f"\n分析分割: {split}")
            
            split_label_dir = self.npy_label_path / f"{split}_label"
            if not split_label_dir.exists():
                print(f"  警告: NPY标签目录不存在: {split_label_dir}")
                continue
            
            split_actions = set()
            split_action_counts = Counter()
            split_file_count = 0
            split_frame_count = 0
            
            # 遍历任务目录
            for task_dir in split_label_dir.iterdir():
                if not task_dir.is_dir():
                    continue
                
                task_name = task_dir.name
                npy_files = list(task_dir.glob("*.npy"))
                
                print(f"  任务 {task_name}: {len(npy_files)} 个NPY标签文件")
                
                for npy_file in npy_files:
                    try:
                        # 加载NPY标签文件
                        labels = np.load(npy_file)
                        
                        # 统计动作类别
                        unique_labels = np.unique(labels)
                        for label in unique_labels:
                            action_id = int(label)
                            split_actions.add(action_id)
                            count = np.sum(labels == label)
                            split_action_counts[action_id] += count
                            self.npy_results['action_counts'][action_id] += count
                        
                        split_file_count += 1
                        split_frame_count += len(labels)
                        
                    except Exception as e:
                        print(f"    警告: 无法加载 {npy_file}: {e}")
            
            # 更新结果
            self.npy_results['split_distribution'][split] = {
                'actions': split_actions,
                'action_counts': dict(split_action_counts),
                'file_count': split_file_count,
                'frame_count': split_frame_count
            }
            
            if split in train_splits:
                self.npy_results['train_actions'].update(split_actions)
            else:
                self.npy_results['test_actions'].update(split_actions)
            
            self.npy_results['all_actions'].update(split_actions)
            self.npy_results['file_count'] += split_file_count
            self.npy_results['total_frames'] += split_frame_count
            
            print(f"  {split} 总计: {len(split_actions)} 个动作类别, {split_file_count} 个文件, {split_frame_count} 帧")
        
        print(f"\nNPY标签分析完成:")
        print(f"  训练集动作类别: {len(self.npy_results['train_actions'])}")
        print(f"  测试集动作类别: {len(self.npy_results['test_actions'])}")
        print(f"  总动作类别: {len(self.npy_results['all_actions'])}")
        print(f"  总文件数: {self.npy_results['file_count']}")
        print(f"  总帧数: {self.npy_results['total_frames']}")

    def find_missing_actions(self):
        """找出缺失的动作类别"""
        print("\n" + "=" * 60)
        print("分析缺失的动作类别")
        print("=" * 60)
        
        all_defined_actions = set(self.label_map.keys())
        missing_actions = all_defined_actions - self.npy_results['all_actions']
        
        print(f"\n📊 动作类别统计:")
        print(f"  定义的动作类别总数: {len(all_defined_actions)}")
        print(f"  NPY文件中的动作类别数: {len(self.npy_results['all_actions'])}")
        print(f"  缺失的动作类别数: {len(missing_actions)}")
        
        if missing_actions:
            print(f"\n❌ 缺失的动作类别:")
            for action_id in sorted(missing_actions):
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                print(f"  • ID {action_id:2d}: {action_name}")
        
        # 检查这些缺失的动作是否在原始txt文件中存在
        print(f"\n🔍 检查缺失动作是否在原始txt文件中存在...")
        self.check_missing_in_original(missing_actions)

    def check_missing_in_original(self, missing_actions: Set[int]):
        """检查缺失的动作是否在原始txt文件中存在"""
        raw_label_path = Path(self.config['data']['raw_label_path'])
        
        found_in_original = {}
        
        for action_id in missing_actions:
            action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
            found_in_original[action_id] = {'name': action_name, 'files': [], 'count': 0}
            
            # 搜索所有原始标签文件
            for split in ['s1', 's2', 's3', 's4']:
                split_label_dir = raw_label_path / f"{split}_label"
                if not split_label_dir.exists():
                    continue
                
                for task_dir in split_label_dir.iterdir():
                    if not task_dir.is_dir():
                        continue
                    
                    for txt_file in task_dir.glob("*.txt"):
                        try:
                            with open(txt_file, 'r', encoding='utf-8') as f:
                                content = f.read()
                                if action_name in content:
                                    found_in_original[action_id]['files'].append(str(txt_file))
                                    # 计算出现次数
                                    count = content.count(action_name)
                                    found_in_original[action_id]['count'] += count
                        except Exception as e:
                            pass
        
        # 报告结果
        print(f"\n📋 缺失动作在原始文件中的存在情况:")
        for action_id in sorted(missing_actions):
            info = found_in_original[action_id]
            if info['files']:
                print(f"  ✅ ID {action_id:2d} ({info['name']}): 在 {len(info['files'])} 个文件中出现 {info['count']} 次")
                # 只显示前3个文件
                for file_path in info['files'][:3]:
                    print(f"      - {file_path}")
                if len(info['files']) > 3:
                    print(f"      ... 还有 {len(info['files']) - 3} 个文件")
            else:
                print(f"  ❌ ID {action_id:2d} ({info['name']}): 在原始文件中未找到")

    def generate_detailed_report(self):
        """生成详细报告"""
        print("\n" + "=" * 80)
        print("NPY标签文件详细分析报告")
        print("=" * 80)
        
        # 各分割的详细统计
        for split, split_data in self.npy_results['split_distribution'].items():
            print(f"\n📊 {split.upper()} 分割统计:")
            print(f"  • 动作类别数: {len(split_data['actions'])}")
            print(f"  • 文件数: {split_data['file_count']}")
            print(f"  • 总帧数: {split_data['frame_count']}")
            
            # 显示前10个最常见的动作
            sorted_actions = sorted(
                split_data['action_counts'].items(),
                key=lambda x: x[1], reverse=True
            )[:10]
            
            print(f"  • 前10个最常见动作:")
            for action_id, count in sorted_actions:
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                print(f"    - ID {action_id:2d}: {action_name:<15} ({count:6d} 帧)")


def main():
    """主函数"""
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建分析器
    analyzer = NpyLabelAnalyzer(config)
    
    # 分析NPY标签文件
    analyzer.analyze_npy_labels()
    
    # 找出缺失的动作类别
    analyzer.find_missing_actions()
    
    # 生成详细报告
    analyzer.generate_detailed_report()
    
    print(f"\n🎯 NPY标签分析完成!")


if __name__ == "__main__":
    main()
