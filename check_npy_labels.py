#!/usr/bin/env python3
"""检查NPY数据和标签的完整性"""

import numpy as np
from pathlib import Path
import json

def check_data_structure():
    """检查数据结构和动作类别"""
    
    # 路径配置
    npy_data_path = Path("/data2/syd_data/Breakfast_Data/breakfast_data_npy")
    npy_label_path = Path("/data2/syd_data/Breakfast_Data/segmentation_coarse_npy")
    label_map_path = Path("/data2/syd_data/Breakfast_Data/label_map.json")
    
    print("=== 检查数据结构 ===")
    
    # 1. 检查label_map.json
    with open(label_map_path, 'r') as f:
        label_map = json.load(f)
    print(f"label_map.json包含 {len(label_map)} 个动作类别")
    print("动作类别列表:")
    for id, action in sorted(label_map.items(), key=lambda x: int(x[0])):
        print(f"  {id}: {action}")
    
    # 2. 检查训练数据结构
    print("\n=== 检查训练数据 ===")
    all_action_ids = set()
    sample_counts = {}
    
    for split in ["s1", "s2", "s3"]:
        split_path = npy_data_path / split
        if not split_path.exists():
            print(f"警告: {split_path} 不存在")
            continue
            
        print(f"\n处理 {split}:")
        
        # 列出所有任务目录
        task_dirs = [d for d in split_path.iterdir() if d.is_dir()]
        print(f"  找到 {len(task_dirs)} 个任务目录: {[d.name for d in task_dirs]}")
        
        for task_dir in task_dirs:
            task_name = task_dir.name
            
            # 检查特征文件
            feature_files = list(task_dir.glob("*.npy"))
            print(f"  {task_name}: {len(feature_files)} 个特征文件")
            
            # 检查标签文件
            label_dir = npy_label_path / f"{split}_label" / task_name
            if label_dir.exists():
                label_files = list(label_dir.glob("*.npy"))
                print(f"    标签文件: {len(label_files)} 个")
                
                # 加载一个标签文件检查动作类别
                if label_files:
                    labels = np.load(label_files[0])
                    unique_actions = np.unique(labels)
                    all_action_ids.update(unique_actions)
                    
                    # 统计每个动作的样本数
                    for action_id in unique_actions:
                        if action_id not in sample_counts:
                            sample_counts[action_id] = 0
                        sample_counts[action_id] += np.sum(labels == action_id)
            else:
                print(f"    警告: 标签目录不存在: {label_dir}")
    
    # 3. 统计结果
    print("\n=== 数据统计 ===")
    print(f"数据中实际出现的动作ID数量: {len(all_action_ids)}")
    print(f"实际动作ID: {sorted(all_action_ids)}")
    
    # 检查是否有未在label_map中的动作
    unmapped_ids = all_action_ids - set(int(k) for k in label_map.keys())
    if unmapped_ids:
        print(f"\n警告: 发现未在label_map中的动作ID: {unmapped_ids}")
    
    # 检查label_map中但数据中没有的动作
    unused_ids = set(int(k) for k in label_map.keys()) - all_action_ids
    if unused_ids:
        print(f"\n警告: label_map中定义但数据中未出现的动作ID: {unused_ids}")
        for id in unused_ids:
            print(f"  {id}: {label_map[str(id)]}")
    
    # 显示每个动作的样本数
    print("\n每个动作的样本数:")
    for action_id in sorted(sample_counts.keys()):
        action_name = label_map.get(str(action_id), "UNKNOWN")
        print(f"  {action_id} ({action_name}): {sample_counts[action_id]} 帧")
    
    return all_action_ids, sample_counts

if __name__ == "__main__":
    check_data_structure()
