#!/usr/bin/env python3
"""详细测试多视角融合过程"""

import sys
import numpy as np
import yaml
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

from utils.data_loader import BreakfastDataLoader, MultiViewFusion

def test_detailed_fusion_process():
    """详细测试多视角融合过程"""
    print("=" * 80)
    print("详细测试多视角融合过程")
    print("=" * 80)
    
    # 加载配置
    with open('configs/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 手动加载多个视角的数据
    data_path = Path(config['data']['npy_data_path'])
    task_dir = data_path / "s1" / "cereals"
    
    print(f"任务目录: {task_dir}")
    
    # 获取所有视角文件
    view_files = sorted([f for f in task_dir.glob("*.npy") if "cereals" in f.name])
    print(f"找到 {len(view_files)} 个视角文件:")
    
    for i, view_file in enumerate(view_files):
        print(f"  {i+1}. {view_file.name}")
    
    # 加载每个视角的数据
    view_features = []
    for view_file in view_files:
        features = np.load(view_file)
        print(f"\n视角文件: {view_file.name}")
        print(f"  原始形状: {features.shape}")
        print(f"  帧索引范围: {features[:, 0].min():.0f} - {features[:, 0].max():.0f}")
        print(f"  特征范围: [{features[:, 1:].min():.3f}, {features[:, 1:].max():.3f}]")
        view_features.append(features)
    
    # 测试帧同步
    print(f"\n" + "-" * 40)
    print("帧同步测试:")
    
    from utils.data_loader import FrameSynchronizer
    synchronizer = FrameSynchronizer()
    
    common_frames = synchronizer.find_common_frames(view_features)
    print(f"  共同帧数量: {len(common_frames)}")
    print(f"  共同帧范围: {min(common_frames)} - {max(common_frames)}")
    
    synced_features = synchronizer.sync_features(view_features, common_frames)
    print(f"  同步后视角数量: {len(synced_features)}")
    
    for i, features in enumerate(synced_features):
        print(f"    视角{i+1}同步后形状: {features.shape}")
    
    # 测试不同融合方法
    print(f"\n" + "-" * 40)
    print("融合方法测试:")
    
    fusion_methods = ["mean", "max", "concat"]
    
    for method in fusion_methods:
        print(f"\n  {method.upper()} 融合:")
        try:
            fusion = MultiViewFusion(fusion_method=method)
            fused_features = fusion.fuse_views(view_features)
            print(f"    融合后形状: {fused_features.shape}")
            print(f"    特征范围: [{fused_features.min():.3f}, {fused_features.max():.3f}]")
            
            # 检查数据质量
            if np.isnan(fused_features).any():
                print("    ✗ 包含NaN值")
            else:
                print("    ✓ 无NaN值")
                
            if np.isinf(fused_features).any():
                print("    ✗ 包含Inf值")
            else:
                print("    ✓ 无Inf值")
                
        except Exception as e:
            print(f"    ✗ 融合失败: {e}")
    
    # 测试数据加载器的完整流程
    print(f"\n" + "-" * 40)
    print("数据加载器完整流程测试:")
    
    data_loader = BreakfastDataLoader(config)
    features, labels = data_loader.load_npy_data("s1", "cereals")
    
    print(f"  最终特征形状: {features.shape}")
    print(f"  最终标签形状: {labels.shape}")
    print(f"  特征维度: {features.shape[1]} (应该是64)")
    
    # 验证融合结果的合理性
    print(f"\n" + "-" * 40)
    print("融合结果验证:")
    
    # 计算原始视角的统计信息
    original_means = []
    for view_data in view_features:
        view_mean = np.mean(view_data[:, 1:], axis=0)  # 排除帧索引列
        original_means.append(view_mean)
    
    # 计算期望的平均值
    expected_mean = np.mean(original_means, axis=0)
    
    # 计算实际融合结果的平均值
    actual_mean = np.mean(features, axis=0)
    
    # 比较差异
    mean_diff = np.abs(expected_mean - actual_mean).mean()
    print(f"  期望平均值与实际平均值的差异: {mean_diff:.6f}")
    
    if mean_diff < 1e-3:
        print("  ✓ 融合结果合理")
    else:
        print("  ⚠ 融合结果可能存在问题")

def test_single_view_case():
    """测试单视角情况"""
    print(f"\n" + "=" * 80)
    print("测试单视角情况")
    print("=" * 80)
    
    # 创建单视角测试数据
    single_view = np.array([
        [1, 0.1, 0.2, 0.3, 0.4],
        [2, 0.5, 0.6, 0.7, 0.8],
        [3, 0.9, 1.0, 1.1, 1.2]
    ])
    
    fusion = MultiViewFusion(fusion_method="mean")
    result = fusion.fuse_views([single_view])
    
    print(f"单视角输入形状: {single_view.shape}")
    print(f"融合后形状: {result.shape}")
    print(f"期望形状: (3, 4)")
    
    # 验证结果
    expected = single_view[:, 1:]  # 去掉帧索引列
    if np.array_equal(result, expected):
        print("✓ 单视角融合结果正确")
    else:
        print("✗ 单视角融合结果错误")

if __name__ == "__main__":
    test_detailed_fusion_process()
    test_single_view_case()
