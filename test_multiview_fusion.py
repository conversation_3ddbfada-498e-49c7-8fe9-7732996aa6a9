#!/usr/bin/env python3
"""测试多视角融合功能"""

import sys
import numpy as np
import yaml
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

from utils.data_loader import BreakfastDataLoader

def test_multiview_fusion():
    """测试多视角融合功能"""
    print("=" * 80)
    print("测试多视角融合功能")
    print("=" * 80)
    
    # 加载配置
    with open('configs/config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建数据加载器
    data_loader = BreakfastDataLoader(config)
    
    # 测试加载数据
    test_cases = [
        ("s1", "cereals"),
        ("s1", "sandwich"),
        ("s4", "cereals")
    ]
    
    for split, task in test_cases:
        print(f"\n测试 {split}/{task}:")
        print("-" * 40)
        
        try:
            # 加载数据
            features, labels = data_loader.load_npy_data(split, task)
            
            print(f"✓ 成功加载数据")
            print(f"  特征形状: {features.shape}")
            print(f"  标签形状: {labels.shape}")
            print(f"  特征维度: {features.shape[1]} (应该是64)")
            print(f"  帧数: {len(features)}")
            print(f"  标签范围: {labels.min()} - {labels.max()}")
            
            # 检查特征维度
            if features.shape[1] == 64:
                print("  ✓ 特征维度正确")
            else:
                print(f"  ✗ 特征维度错误，期望64，实际{features.shape[1]}")
            
            # 检查数据类型
            print(f"  特征数据类型: {features.dtype}")
            print(f"  标签数据类型: {labels.dtype}")
            
            # 检查数据范围
            print(f"  特征值范围: [{features.min():.3f}, {features.max():.3f}]")
            
            # 检查是否有NaN或Inf
            if np.isnan(features).any():
                print("  ✗ 特征中包含NaN值")
            else:
                print("  ✓ 特征中无NaN值")
                
            if np.isinf(features).any():
                print("  ✗ 特征中包含Inf值")
            else:
                print("  ✓ 特征中无Inf值")
            
        except Exception as e:
            print(f"✗ 加载失败: {e}")
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)

def test_view_identification():
    """测试视角识别功能"""
    print("\n测试视角识别功能:")
    print("-" * 40)
    
    from utils.data_loader import ViewIdentifier
    
    view_identifier = ViewIdentifier()
    
    test_files = [
        "P03_cam01_P03_cereals.npy",
        "P03_stereo01_P03_cereals.npy", 
        "P03_webcam01_P03_cereals.npy",
        "P03_webcam02_P03_cereals.npy"
    ]
    
    for file_path in test_files:
        view_id = view_identifier.extract_view_id(file_path)
        print(f"  {file_path} -> 视角ID: {view_id}")
    
    # 测试排序
    sorted_files = view_identifier.sort_by_view(test_files)
    print(f"\n  排序后的文件:")
    for i, file_path in enumerate(sorted_files):
        print(f"    {i+1}. {file_path}")

def test_frame_synchronization():
    """测试帧同步功能"""
    print("\n测试帧同步功能:")
    print("-" * 40)
    
    from utils.data_loader import FrameSynchronizer
    
    # 创建测试数据
    view1 = np.array([
        [1, 0.1, 0.2, 0.3],  # 帧1
        [2, 0.4, 0.5, 0.6],  # 帧2
        [3, 0.7, 0.8, 0.9],  # 帧3
    ])
    
    view2 = np.array([
        [1, 1.1, 1.2, 1.3],  # 帧1
        [3, 1.7, 1.8, 1.9],  # 帧3 (缺少帧2)
        [4, 2.0, 2.1, 2.2],  # 帧4
    ])
    
    view_features = [view1, view2]
    
    # 找到共同帧
    common_frames = FrameSynchronizer.find_common_frames(view_features)
    print(f"  共同帧: {common_frames}")
    
    # 同步特征
    synced_features = FrameSynchronizer.sync_features(view_features, common_frames)
    print(f"  同步后的特征数量: {len(synced_features)}")
    for i, features in enumerate(synced_features):
        print(f"    视角{i+1}形状: {features.shape}")

if __name__ == "__main__":
    test_multiview_fusion()
    test_view_identification()
    test_frame_synchronization()
