#!/usr/bin/env python3
"""
原始数据集分析脚本 - check_original_data.py
分析原始txt格式数据中的动作类别分布，排查是否在转换过程中丢失了类别

主要功能：
1. 分析原始txt格式标签文件中的动作类别
2. 对比原始数据与转换后NPY数据的类别差异
3. 确定类别缺失是原始数据问题还是转换过程问题
"""

import sys
import json
import yaml
from pathlib import Path
from typing import Dict, Set, List
from collections import defaultdict, Counter
import re

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class OriginalDataAnalyzer:
    """原始数据分析器"""

    def __init__(self, config: Dict):
        self.config = config
        
        # 路径配置
        self.raw_label_path = Path(config['data']['raw_label_path'])
        self.label_map_path = Path(config['data']['label_map_path'])
        
        # 加载标签映射
        self.label_map = self._load_label_map()
        self.action_to_id = {v: k for k, v in self.label_map.items()}
        
        # 分析结果
        self.original_results = {
            'train_actions': set(),
            'test_actions': set(),
            'all_actions': set(),
            'action_counts': Counter(),
            'split_distribution': defaultdict(lambda: defaultdict(int)),
            'unknown_actions': set(),
            'parsing_errors': []
        }

    def _load_label_map(self) -> Dict[int, str]:
        """加载标签映射文件"""
        if not self.label_map_path.exists():
            print(f"错误: 标签映射文件不存在: {self.label_map_path}")
            return {}
        
        with open(self.label_map_path, 'r', encoding='utf-8') as f:
            label_data = json.load(f)
        
        # 转换为 {id: name} 格式
        if isinstance(label_data, dict):
            if all(isinstance(k, (int, str)) and isinstance(v, str) for k, v in label_data.items()):
                return {int(k): v for k, v in label_data.items()}
            elif all(isinstance(k, str) and isinstance(v, (int, str)) for k, v in label_data.items()):
                return {int(v): k for k, v in label_data.items()}
        
        return {}

    def parse_label_file(self, label_file: Path) -> List[str]:
        """解析单个标签文件，提取所有动作名称"""
        actions = []
        
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 解析格式: "1-30 SIL" 或 "31-150 take_bowl"
                    parts = line.split()
                    if len(parts) >= 2:
                        # 第一部分是帧范围，第二部分开始是动作名
                        action_name = ' '.join(parts[1:]).strip()
                        actions.append(action_name)
                    else:
                        self.original_results['parsing_errors'].append(
                            f"{label_file}:{line_num} - 无法解析: {line}"
                        )
        
        except Exception as e:
            self.original_results['parsing_errors'].append(
                f"{label_file} - 读取错误: {e}"
            )
        
        return actions

    def analyze_original_labels(self):
        """分析原始标签文件"""
        print("=" * 60)
        print("开始分析原始txt格式标签数据")
        print("=" * 60)
        
        # 定义训练和测试分割
        train_splits = ['s1', 's2', 's3']
        test_splits = ['s4']
        
        all_splits = train_splits + test_splits
        
        for split in all_splits:
            print(f"\n分析分割: {split}")
            
            # 构建标签路径
            split_label_dir = self.raw_label_path / f"{split}_label"
            
            if not split_label_dir.exists():
                print(f"  警告: 标签目录不存在: {split_label_dir}")
                continue
            
            split_actions = set()
            split_action_counts = Counter()
            
            # 遍历任务目录或直接查找标签文件
            label_files = []
            
            # 检查是否有任务子目录
            task_dirs = [d for d in split_label_dir.iterdir() if d.is_dir()]
            if task_dirs:
                # 有任务子目录的情况
                for task_dir in task_dirs:
                    task_name = task_dir.name
                    task_label_files = list(task_dir.glob("*.txt"))
                    label_files.extend(task_label_files)
                    print(f"  任务 {task_name}: {len(task_label_files)} 个标签文件")
            else:
                # 直接在split_label目录下查找txt文件
                label_files = list(split_label_dir.glob("*.txt"))
                print(f"  直接找到 {len(label_files)} 个标签文件")
            
            # 解析所有标签文件
            for label_file in label_files:
                actions = self.parse_label_file(label_file)
                
                for action_name in actions:
                    # 统一转换为小写进行比较
                    action_name_lower = action_name.lower()
                    
                    # 检查是否在标签映射中
                    if action_name in self.action_to_id:
                        action_id = self.action_to_id[action_name]
                        split_actions.add(action_id)
                        split_action_counts[action_id] += 1
                        self.original_results['action_counts'][action_id] += 1
                    elif action_name_lower in [v.lower() for v in self.action_to_id.keys()]:
                        # 大小写不匹配，找到对应的动作
                        for orig_name, action_id in self.action_to_id.items():
                            if orig_name.lower() == action_name_lower:
                                split_actions.add(action_id)
                                split_action_counts[action_id] += 1
                                self.original_results['action_counts'][action_id] += 1
                                break
                    else:
                        # 未知动作
                        self.original_results['unknown_actions'].add(action_name)
                        print(f"    警告: 未知动作 '{action_name}' 在文件 {label_file}")
            
            # 更新结果
            self.original_results['split_distribution'][split] = dict(split_action_counts)
            
            if split in train_splits:
                self.original_results['train_actions'].update(split_actions)
            else:
                self.original_results['test_actions'].update(split_actions)
            
            self.original_results['all_actions'].update(split_actions)
            
            print(f"  {split} 总计: {len(split_actions)} 个动作类别")
        
        print(f"\n原始数据分析完成:")
        print(f"  训练集动作类别: {len(self.original_results['train_actions'])}")
        print(f"  测试集动作类别: {len(self.original_results['test_actions'])}")
        print(f"  总动作类别: {len(self.original_results['all_actions'])}")
        print(f"  未知动作: {len(self.original_results['unknown_actions'])}")
        print(f"  解析错误: {len(self.original_results['parsing_errors'])}")

    def compare_with_converted_data(self):
        """与转换后的数据进行对比"""
        print("\n" + "=" * 60)
        print("对比原始数据与转换后数据")
        print("=" * 60)
        
        # 从之前的分析结果中读取转换后的数据
        converted_file = Path("action_analysis_detailed.json")
        if not converted_file.exists():
            print("警告: 未找到转换后数据的分析结果文件")
            return
        
        with open(converted_file, 'r', encoding='utf-8') as f:
            converted_data = json.load(f)
        
        # 提取转换后的动作类别
        converted_train_actions = set()
        converted_test_actions = set()
        
        for action_info in converted_data['found_actions']:
            action_id = action_info['id']
            split_dist = action_info['split_distribution']
            
            # 检查是否在训练集中出现
            if any(split_dist.get(split, 0) > 0 for split in ['s1', 's2', 's3']):
                converted_train_actions.add(action_id)
            
            # 检查是否在测试集中出现（需要重新分析测试集）
            # 这里暂时使用之前check_type.py的结果
        
        # 简化对比：直接使用已知结果
        converted_train_actions = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 20, 21, 23, 25, 26, 27, 28, 30, 33, 36, 38, 39, 41, 43, 44, 46}  # 从之前结果得出
        converted_all_actions = converted_train_actions.union({34, 45})  # 加上测试集独有的
        
        # 对比分析
        print(f"\n📊 对比结果:")
        print(f"  原始数据 - 训练集类别数: {len(self.original_results['train_actions'])}")
        print(f"  转换数据 - 训练集类别数: {len(converted_train_actions)}")
        print(f"  原始数据 - 总类别数: {len(self.original_results['all_actions'])}")
        print(f"  转换数据 - 总类别数: {len(converted_all_actions)}")
        
        # 检查是否有类别在转换过程中丢失
        lost_in_conversion = self.original_results['all_actions'] - converted_all_actions
        gained_in_conversion = converted_all_actions - self.original_results['all_actions']
        
        if lost_in_conversion:
            print(f"\n❌ 转换过程中丢失的类别 ({len(lost_in_conversion)} 个):")
            for action_id in sorted(lost_in_conversion):
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                print(f"    • ID {action_id}: {action_name}")
        
        if gained_in_conversion:
            print(f"\n➕ 转换过程中新增的类别 ({len(gained_in_conversion)} 个):")
            for action_id in sorted(gained_in_conversion):
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                print(f"    • ID {action_id}: {action_name}")
        
        if not lost_in_conversion and not gained_in_conversion:
            print(f"\n✅ 转换过程完整: 没有类别丢失或新增")
        
        # 检查未知动作
        if self.original_results['unknown_actions']:
            print(f"\n⚠️  原始数据中的未知动作 ({len(self.original_results['unknown_actions'])} 个):")
            for action_name in sorted(self.original_results['unknown_actions']):
                print(f"    • {action_name}")

    def generate_report(self) -> str:
        """生成详细报告"""
        report = []
        report.append("=" * 80)
        report.append("原始数据集分析报告")
        report.append("=" * 80)
        
        # 基本统计
        report.append(f"\n📊 原始数据统计:")
        report.append(f"  • 定义的动作类别总数: {len(self.label_map)}")
        report.append(f"  • 原始训练集中的类别数: {len(self.original_results['train_actions'])}")
        report.append(f"  • 原始测试集中的类别数: {len(self.original_results['test_actions'])}")
        report.append(f"  • 原始数据总类别数: {len(self.original_results['all_actions'])}")
        report.append(f"  • 未知动作数: {len(self.original_results['unknown_actions'])}")
        
        # 缺失类别分析
        all_defined_actions = set(self.label_map.keys())
        missing_in_original = all_defined_actions - self.original_results['all_actions']
        
        if missing_in_original:
            report.append(f"\n❌ 原始数据中缺失的类别 ({len(missing_in_original)} 个):")
            for action_id in sorted(missing_in_original):
                action_name = self.label_map.get(action_id, f"未知动作_{action_id}")
                report.append(f"  • ID {action_id:2d}: {action_name}")
        
        # 解析错误
        if self.original_results['parsing_errors']:
            report.append(f"\n⚠️  解析错误 ({len(self.original_results['parsing_errors'])} 个):")
            for error in self.original_results['parsing_errors'][:10]:  # 只显示前10个
                report.append(f"  • {error}")
            if len(self.original_results['parsing_errors']) > 10:
                report.append(f"  ... 还有 {len(self.original_results['parsing_errors']) - 10} 个错误")
        
        return "\n".join(report)


def main():
    """主函数"""
    # 加载配置
    config_path = Path(__file__).parent / "configs" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建分析器
    analyzer = OriginalDataAnalyzer(config)
    
    # 分析原始数据
    analyzer.analyze_original_labels()
    
    # 与转换后数据对比
    analyzer.compare_with_converted_data()
    
    # 生成报告
    report = analyzer.generate_report()
    print(report)
    
    print(f"\n🎯 原始数据分析完成!")


if __name__ == "__main__":
    main()
